"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/data.ts":
/*!*************************!*\
  !*** ./src/lib/data.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   blogPosts: () => (/* binding */ blogPosts),\n/* harmony export */   projects: () => (/* binding */ projects),\n/* harmony export */   services: () => (/* binding */ services),\n/* harmony export */   statistics: () => (/* binding */ statistics),\n/* harmony export */   teamMembers: () => (/* binding */ teamMembers)\n/* harmony export */ });\nconst services = [\n    {\n        id: '1',\n        title: 'Automatización Industrial',\n        description: 'Sistemas de automatización completos para optimizar procesos industriales y aumentar la eficiencia operativa.',\n        icon: 'Settings',\n        features: [\n            'Diseño de sistemas automatizados',\n            'Integración de sensores y actuadores',\n            'Optimización de procesos',\n            'Reducción de costos operativos'\n        ]\n    },\n    {\n        id: '2',\n        title: 'Robótica Industrial',\n        description: 'Implementación de soluciones robóticas avanzadas para manufactura y manipulación de materiales.',\n        icon: 'Bot',\n        features: [\n            'Robots colaborativos (cobots)',\n            'Sistemas de visión artificial',\n            'Programación de trayectorias',\n            'Mantenimiento predictivo'\n        ]\n    },\n    {\n        id: '3',\n        title: 'Programación PLC/SCADA',\n        description: 'Desarrollo y programación de sistemas de control distribuido para monitoreo y control de procesos industriales.',\n        icon: 'Code',\n        features: [\n            'Programación de PLCs',\n            'Desarrollo de interfaces SCADA',\n            'Sistemas de alarmas',\n            'Históricos y reportes'\n        ]\n    },\n    {\n        id: '4',\n        title: 'Cuadros Eléctricos',\n        description: 'Diseño, fabricación e instalación de cuadros eléctricos industriales con los más altos estándares de calidad.',\n        icon: 'Zap',\n        features: [\n            'Diseño según normativas',\n            'Fabricación personalizada',\n            'Instalación y puesta en marcha',\n            'Certificaciones de calidad'\n        ]\n    },\n    {\n        id: '5',\n        title: 'Consultoría Técnica',\n        description: 'Asesoramiento especializado en ingeniería industrial, optimización de procesos y transformación digital.',\n        icon: 'Users',\n        features: [\n            'Auditorías técnicas',\n            'Planes de modernización',\n            'Análisis de eficiencia',\n            'Formación especializada'\n        ]\n    },\n    {\n        id: '6',\n        title: 'Mantenimiento Industrial',\n        description: 'Servicios integrales de mantenimiento preventivo, predictivo y correctivo para equipos industriales.',\n        icon: 'Wrench',\n        features: [\n            'Mantenimiento preventivo',\n            'Diagnóstico predictivo',\n            'Reparaciones especializadas',\n            'Gestión de repuestos'\n        ]\n    }\n];\nconst projects = [\n    {\n        id: '1',\n        title: 'Automatización de Línea de Tomate Concentrado',\n        description: 'Optimización completa de la línea de producción de tomate concentrado mediante control automatizado y monitoreo en tiempo real.',\n        image: '/imagenes/1.jpg',\n        category: 'Alimentaria',\n        technologies: [\n            'Siemens Step7',\n            'Profibus',\n            'Intouch',\n            'SCADA',\n            'PLC'\n        ],\n        year: 2025,\n        client: 'Conesa Group'\n    },\n    {\n        id: '2',\n        title: 'Producción Automatizada de Tomate Frito',\n        description: 'Automatización integral del proceso de tomate frito, desde la cocción hasta el envasado final, garantizando calidad constante.',\n        image: '/imagenes/7.jpg',\n        category: 'Alimentaria',\n        technologies: [\n            'CX-Programmer',\n            'CX-Supervisor',\n            'Weidmuller',\n            'Instrumentación'\n        ],\n        year: 2024,\n        client: 'Tomcoex'\n    },\n    {\n        id: '3',\n        title: 'Sistema de Filtrado de Arena Industrial',\n        description: 'Automatización de filtrado industrial con control de presión y ciclos de retrolavado automáticos para mayor eficiencia.',\n        image: '/imagenes/6.jpg',\n        category: 'Fluidos',\n        technologies: [\n            'PLC Omron',\n            'CX-Supervisor',\n            'HMI Proface',\n            'Válvulas Automáticas'\n        ],\n        year: 2025,\n        client: 'Agraz'\n    },\n    {\n        id: '4',\n        title: 'Línea Automatizada de Pelado de Almendras',\n        description: 'Control automático de rodillos y detección de obstrucciones para un pelado seguro y eficiente.',\n        image: '/imagenes/5.jpg',\n        category: 'Alimentaria',\n        technologies: [\n            'PLC Omron',\n            'HMI Proface',\n            'Variadores de Frecuencia'\n        ],\n        year: 2024,\n        client: 'Acopaex'\n    },\n    {\n        id: '5',\n        title: 'Producción Automatizada de Sustratos',\n        description: 'Línea de mezclado y envasado de sustratos con control de pesaje, humedad y flujo de material optimizado.',\n        image: '/imagenes/3.jpg',\n        category: 'Industrial',\n        technologies: [\n            'Siemens S7-1200',\n            'Profinet',\n            'Arrancador Schneider',\n            'Variadores'\n        ],\n        year: 2023,\n        client: 'Sustratos de Extremadura'\n    },\n    {\n        id: '6',\n        title: 'Pasadoras y Refinadoras de Tomate Automatizadas',\n        description: 'Control preciso de caudal y proceso en pasadoras y refinadoras para garantizar la calidad del producto final.',\n        image: '/imagenes/8.jpg',\n        category: 'Alimentaria',\n        technologies: [\n            'PLC Omron',\n            'HMI Proface',\n            'Variadores de Frecuencia'\n        ],\n        year: 2024,\n        client: 'Conesa Group'\n    },\n    {\n        id: '7',\n        title: 'Sistema de Extracción de gases',\n        description: 'Automatización de ventilación industrial con control de caudal, presión y alarmas de seguridad en tiempo real.',\n        image: '/imagenes/4.jpg',\n        category: 'Fluidos',\n        technologies: [\n            'PLC Simenes 1500',\n            'Redundancia',\n            'HMI Unified',\n            'Valvulas Automáticas'\n        ],\n        year: 2024,\n        client: 'Diamond Foundry'\n    }\n];\nconst blogPosts = [\n    {\n        id: '1',\n        title: 'El Futuro de la Automatización Industrial',\n        excerpt: 'Exploramos las tendencias emergentes en automatización y cómo están transformando la industria.',\n        content: '',\n        image: '/post/11.jpg',\n        author: 'Equipo Servindex',\n        publishedAt: '2024-01-15',\n        category: 'Automatización',\n        tags: [\n            'Industria 4.0',\n            'IoT',\n            'Automatización'\n        ],\n        readTime: 5\n    },\n    {\n        id: '2',\n        title: 'Mantenimiento Predictivo con IA',\n        excerpt: 'Cómo la inteligencia artificial está revolucionando el mantenimiento industrial.',\n        content: '',\n        image: '/post/2.jpg',\n        author: 'Equipo Servindex',\n        publishedAt: '2024-01-10',\n        category: 'Mantenimiento',\n        tags: [\n            'IA',\n            'Mantenimiento',\n            'Predictivo'\n        ],\n        readTime: 7\n    },\n    {\n        id: '3',\n        title: 'Robots Colaborativos en la Industria',\n        excerpt: 'Los cobots están cambiando la forma de trabajar en fábricas y talleres.',\n        content: '',\n        image: '/post/3.jpg',\n        author: 'Equipo Servindex',\n        publishedAt: '2024-02-01',\n        category: 'Robótica',\n        tags: [\n            'Cobots',\n            'Automatización',\n            'Industria 4.0'\n        ],\n        readTime: 6\n    },\n    {\n        id: '4',\n        title: 'Sensores Inteligentes y Control en Tiempo Real',\n        excerpt: 'La importancia de la sensórica avanzada para mejorar la eficiencia de las líneas de producción.',\n        content: '',\n        image: '/post/4.jpg',\n        author: 'Equipo Servindex',\n        publishedAt: '2024-02-05',\n        category: 'Automatización',\n        tags: [\n            'Sensores',\n            'Control Industrial',\n            'Eficiencia'\n        ],\n        readTime: 5\n    },\n    {\n        id: '5',\n        title: 'Transformación Digital en Fábricas',\n        excerpt: 'Cómo digitalizar procesos industriales y aumentar la competitividad.',\n        content: '',\n        image: '/post/5.jpg',\n        author: 'Equipo Servindex',\n        publishedAt: '2024-02-10',\n        category: 'Transformación Digital',\n        tags: [\n            'Digitalización',\n            'IoT',\n            'Industria'\n        ],\n        readTime: 6\n    },\n    {\n        id: '6',\n        title: 'SCADA y Monitoreo Remoto',\n        excerpt: 'Supervisión de procesos industriales desde cualquier lugar con sistemas SCADA modernos.',\n        content: '',\n        image: '/post/6.jpg',\n        author: 'Equipo Servindex',\n        publishedAt: '2024-02-15',\n        category: 'Control Industrial',\n        tags: [\n            'SCADA',\n            'Monitoreo',\n            'Automatización'\n        ],\n        readTime: 5\n    },\n    {\n        id: '7',\n        title: 'Ciberseguridad en Sistemas Industriales',\n        excerpt: 'Proteger instalaciones industriales frente a amenazas digitales es clave en la Industria 4.0.',\n        content: '',\n        image: '/post/7.jpg',\n        author: 'Equipo Servindex',\n        publishedAt: '2024-02-20',\n        category: 'Seguridad Industrial',\n        tags: [\n            'Ciberseguridad',\n            'Industria 4.0',\n            'Protección'\n        ],\n        readTime: 8\n    },\n    {\n        id: '8',\n        title: 'Eficiencia Energética en la Industria',\n        excerpt: 'Estrategias y tecnologías para reducir el consumo energético de las fábricas.',\n        content: '',\n        image: '/post/8.jpg',\n        author: 'Equipo Servindex',\n        publishedAt: '2024-03-01',\n        category: 'Energía',\n        tags: [\n            'Eficiencia',\n            'Ahorro Energético',\n            'Industria'\n        ],\n        readTime: 6\n    },\n    {\n        id: '9',\n        title: 'Automatización de Líneas de Envasado',\n        excerpt: 'Optimizando la producción con sistemas automáticos en líneas de envasado.',\n        content: '',\n        image: '/post/9.jpg',\n        author: 'Equipo Servindex',\n        publishedAt: '2024-03-05',\n        category: 'Automatización',\n        tags: [\n            'Envasado',\n            'Automatización',\n            'Producción'\n        ],\n        readTime: 5\n    },\n    {\n        id: '10',\n        title: 'Gemelos Digitales en la Industria',\n        excerpt: 'Simulación avanzada de procesos industriales para reducir riesgos y costos.',\n        content: '',\n        image: '/post/10.jpg',\n        author: 'Equipo Servindex',\n        publishedAt: '2024-03-10',\n        category: 'Innovación',\n        tags: [\n            'Gemelo Digital',\n            'Industria 4.0',\n            'Simulación'\n        ],\n        readTime: 7\n    }\n];\nconst teamMembers = [\n    {\n        id: '1',\n        name: 'Pablo Rebollo',\n        position: 'Director Técnico',\n        image: '/imagenes/pablo.png',\n        bio: 'Técnico polivalente en todas las áreas de la automatización y robótica industrial.'\n    },\n    {\n        id: '2',\n        name: 'Jaime Villafruela',\n        position: 'Director Técnico',\n        image: 'https://picsum.photos/400/400?random=7',\n        bio: 'Especialista en sistemas PLC/SCADA con amplia experiencia en proyectos de gran envergadura.'\n    }\n];\nconst statistics = [\n    {\n        id: '1',\n        value: 50,\n        label: 'Proyectos Completados',\n        suffix: '+'\n    },\n    {\n        id: '2',\n        value: 98,\n        label: 'Satisfacción del Cliente',\n        suffix: '%'\n    },\n    {\n        id: '3',\n        value: 10,\n        label: 'Años de Experiencia',\n        suffix: '+'\n    },\n    {\n        id: '4',\n        value: 24,\n        label: 'Soporte Técnico',\n        suffix: '/7'\n    }\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/data.ts\n"));

/***/ })

});