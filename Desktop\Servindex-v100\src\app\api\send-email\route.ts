import { NextRequest, NextResponse } from 'next/server';
import { validateEmailData, createEmailContent, sendWithGmailSMTP, type EmailData } from '@/lib/email';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const emailData: EmailData = {
      name: body.name,
      email: body.email,
      company: body.company,
      phone: body.phone,
      service: body.service,
      message: body.message,
    };

    // Validate the data
    const validation = validateEmailData(emailData);
    if (!validation.isValid) {
      return NextResponse.json(
        { error: 'Datos inválidos', details: validation.errors },
        { status: 400 }
      );
    }

    // Create email content
    const emailContent = createEmailContent(emailData);

    // Send email using Gmail SMTP
    const result = await sendWithGmailSMTP(emailContent);

    if (!result.success) {
      return NextResponse.json(
        { error: `Error al enviar email: ${result.error}` },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Email enviado correctamente',
      messageId: result.messageId
    });

  } catch (error) {
    console.error('Error sending email:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
