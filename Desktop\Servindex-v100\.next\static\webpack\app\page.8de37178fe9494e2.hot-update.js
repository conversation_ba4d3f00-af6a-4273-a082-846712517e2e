"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/data.ts":
/*!*************************!*\
  !*** ./src/lib/data.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   blogPosts: () => (/* binding */ blogPosts),\n/* harmony export */   projects: () => (/* binding */ projects),\n/* harmony export */   services: () => (/* binding */ services),\n/* harmony export */   statistics: () => (/* binding */ statistics),\n/* harmony export */   teamMembers: () => (/* binding */ teamMembers)\n/* harmony export */ });\nconst services = [\n    {\n        id: '1',\n        title: 'Automatización Industrial',\n        description: 'Sistemas de automatización completos para optimizar procesos industriales y aumentar la eficiencia operativa.',\n        icon: 'Settings',\n        features: [\n            'Diseño de sistemas automatizados',\n            'Integración de sensores y actuadores',\n            'Optimización de procesos',\n            'Reducción de costos operativos'\n        ]\n    },\n    {\n        id: '2',\n        title: 'Robótica Industrial',\n        description: 'Implementación de soluciones robóticas avanzadas para manufactura y manipulación de materiales.',\n        icon: 'Bot',\n        features: [\n            'Robots colaborativos (cobots)',\n            'Sistemas de visión artificial',\n            'Programación de trayectorias',\n            'Mantenimiento predictivo'\n        ]\n    },\n    {\n        id: '3',\n        title: 'Programación PLC/SCADA',\n        description: 'Desarrollo y programación de sistemas de control distribuido para monitoreo y control de procesos industriales.',\n        icon: 'Code',\n        features: [\n            'Programación de PLCs',\n            'Desarrollo de interfaces SCADA',\n            'Sistemas de alarmas',\n            'Históricos y reportes'\n        ]\n    },\n    {\n        id: '4',\n        title: 'Cuadros Eléctricos',\n        description: 'Diseño, fabricación e instalación de cuadros eléctricos industriales con los más altos estándares de calidad.',\n        icon: 'Zap',\n        features: [\n            'Diseño según normativas',\n            'Fabricación personalizada',\n            'Instalación y puesta en marcha',\n            'Certificaciones de calidad'\n        ]\n    },\n    {\n        id: '5',\n        title: 'Consultoría Técnica',\n        description: 'Asesoramiento especializado en ingeniería industrial, optimización de procesos y transformación digital.',\n        icon: 'Users',\n        features: [\n            'Auditorías técnicas',\n            'Planes de modernización',\n            'Análisis de eficiencia',\n            'Formación especializada'\n        ]\n    },\n    {\n        id: '6',\n        title: 'Mantenimiento Industrial',\n        description: 'Servicios integrales de mantenimiento preventivo, predictivo y correctivo para equipos industriales.',\n        icon: 'Wrench',\n        features: [\n            'Mantenimiento preventivo',\n            'Diagnóstico predictivo',\n            'Reparaciones especializadas',\n            'Gestión de repuestos'\n        ]\n    }\n];\nconst projects = [\n    {\n        id: '1',\n        title: 'Automatización de Línea de Tomate Concentrado',\n        description: 'Optimización completa de la línea de producción de tomate concentrado mediante control automatizado y monitoreo en tiempo real.',\n        image: '/imagenes/1.jpg',\n        category: 'Alimentaria',\n        technologies: [\n            'Siemens Step7',\n            'Profibus',\n            'Intouch',\n            'SCADA',\n            'PLC'\n        ],\n        year: 2025,\n        client: 'Conesa Group'\n    },\n    {\n        id: '3',\n        title: 'Producción Automatizada de Tomate Frito',\n        description: 'Automatización integral del proceso de tomate frito, desde la cocción hasta el envasado final, garantizando calidad constante.',\n        image: '/imagenes/7.jpg',\n        category: 'Alimentaria',\n        technologies: [\n            'CX-Programmer',\n            'CX-Supervisor',\n            'Weidmuller',\n            'Instrumentación'\n        ],\n        year: 2025,\n        client: 'Tomcoex'\n    },\n    {\n        id: '4',\n        title: 'Línea Robotizada para Producción de Piezas',\n        description: 'Implementación de celdas robotizadas con sistemas de visión para fabricación de piezas con máxima precisión.',\n        image: 'https://picsum.photos/800/600?random=4',\n        category: 'Industrial',\n        technologies: [\n            'KUKA KRC4',\n            'ABB RobotStudio',\n            'PLC Siemens',\n            'Profinet'\n        ],\n        year: 2025,\n        client: 'Santiago Apostol'\n    },\n    {\n        id: '5',\n        title: 'Riego Inteligente con Control Remoto',\n        description: 'Sistema de riego automatizado con monitoreo remoto y programación flexible basado en condiciones del terreno.',\n        image: 'https://picsum.photos/800/600?random=5',\n        category: 'Fluidos',\n        technologies: [\n            'Arduino',\n            'Raspberry Pi',\n            'Flask',\n            'MQTT',\n            'Sensores de Humedad'\n        ],\n        year: 2025,\n        client: 'Elecex'\n    },\n    {\n        id: '6',\n        title: 'Transformación de Uva en Mosto Automatizada',\n        description: 'Control automático del prensado, filtrado y transferencia de mosto para optimizar la producción vitivinícola.',\n        image: 'https://picsum.photos/800/600?random=6',\n        category: 'Alimentaria',\n        technologies: [\n            'Siemens S7-1200',\n            'HMI Unified',\n            'Profinet',\n            'Variadores'\n        ],\n        year: 2025,\n        client: 'Cave San José'\n    },\n    {\n        id: '7',\n        title: 'Sistema de Filtrado de Arena Industrial',\n        description: 'Automatización de filtrado industrial con control de presión y ciclos de retrolavado automáticos para mayor eficiencia.',\n        image: '/imagenes/6.jpg',\n        category: 'Fluidos',\n        technologies: [\n            'PLC Omron',\n            'CX-Supervisor',\n            'HMI Proface',\n            'Válvulas Automáticas'\n        ],\n        year: 2025,\n        client: 'Agraz'\n    },\n    {\n        id: '8',\n        title: 'Línea Automatizada de Pelado de Almendras',\n        description: 'Control automático de rodillos y detección de obstrucciones para un pelado seguro y eficiente.',\n        image: '/imagenes/5.jpg',\n        category: 'Alimentaria',\n        technologies: [\n            'PLC Omron',\n            'HMI Proface',\n            'Variadores de Frecuencia'\n        ],\n        year: 2025,\n        client: 'Acopaex'\n    },\n    {\n        id: '9',\n        title: 'Producción Automatizada de Sustratos',\n        description: 'Línea de mezclado y envasado de sustratos con control de pesaje, humedad y flujo de material optimizado.',\n        image: '/imagenes/3.jpg',\n        category: 'Industrial',\n        technologies: [\n            'Siemens S7-1200',\n            'Profinet',\n            'Arrancador Schneider',\n            'Variadores'\n        ],\n        year: 2025,\n        client: 'Sustratos de Extremadura'\n    },\n    {\n        id: '10',\n        title: 'Pasadoras y Refinadoras de Tomate Automatizadas',\n        description: 'Control preciso de caudal y proceso en pasadoras y refinadoras para garantizar la calidad del producto final.',\n        image: '/imagenes/8.jpg',\n        category: 'Alimentaria',\n        technologies: [\n            'PLC Omron',\n            'HMI Proface',\n            'Variadores de Frecuencia'\n        ],\n        year: 2025,\n        client: 'Conesa Group'\n    },\n    {\n        id: '11',\n        title: 'Sistema de Extracción de gases',\n        description: 'Automatización de ventilación industrial con control de caudal, presión y alarmas de seguridad en tiempo real.',\n        image: '/imagenes/4.jpg',\n        category: 'Fluidos',\n        technologies: [\n            'PLC Simenes 1500',\n            'Redundancia',\n            'HMI Unified',\n            'Valvulas Automáticas'\n        ],\n        year: 2025,\n        client: 'Diamond Foundry'\n    },\n    {\n        id: '12',\n        title: 'Control de Nivel y Bombeo con Grupo de Presión',\n        description: 'Sistema de control automático de nivel y bombeo presurizado con alarmas para evitar funcionamiento en vacío.',\n        image: 'https://picsum.photos/800/600?random=12',\n        category: 'Fluidos',\n        technologies: [\n            'PLC Siemens',\n            'HMI',\n            'Sensores de Nivel',\n            'Variadores de Frecuencia'\n        ],\n        year: 2025,\n        client: 'Diamond Foundry'\n    }\n];\nconst blogPosts = [\n    {\n        id: '1',\n        title: 'El Futuro de la Automatización Industrial',\n        excerpt: 'Exploramos las tendencias emergentes en automatización y cómo están transformando la industria.',\n        content: '',\n        image: 'https://picsum.photos/800/400?random=4',\n        author: 'Equipo Servindex',\n        publishedAt: '2024-01-15',\n        category: 'Automatización',\n        tags: [\n            'Industria 4.0',\n            'IoT',\n            'Automatización'\n        ],\n        readTime: 5\n    },\n    {\n        id: '2',\n        title: 'Mantenimiento Predictivo con IA',\n        excerpt: 'Cómo la inteligencia artificial está revolucionando el mantenimiento industrial.',\n        content: '',\n        image: 'https://picsum.photos/800/400?random=5',\n        author: 'Equipo Servindex',\n        publishedAt: '2024-01-10',\n        category: 'Mantenimiento',\n        tags: [\n            'IA',\n            'Mantenimiento',\n            'Predictivo'\n        ],\n        readTime: 7\n    }\n];\nconst teamMembers = [\n    {\n        id: '1',\n        name: 'Pablo Rebollo',\n        position: 'Director Técnico',\n        image: 'https://picsum.photos/400/400?random=6',\n        bio: 'Técnico Industrial con más de 6 años de experiencia en automatización industrial.'\n    },\n    {\n        id: '2',\n        name: 'Jaime Villafruela',\n        position: 'Director Técnico',\n        image: 'https://picsum.photos/400/400?random=7',\n        bio: 'Especialista en sistemas PLC/SCADA con amplia experiencia en proyectos de gran envergadura.'\n    }\n];\nconst statistics = [\n    {\n        id: '1',\n        value: 50,\n        label: 'Proyectos Completados',\n        suffix: '+'\n    },\n    {\n        id: '2',\n        value: 98,\n        label: 'Satisfacción del Cliente',\n        suffix: '%'\n    },\n    {\n        id: '3',\n        value: 10,\n        label: 'Años de Experiencia',\n        suffix: '+'\n    },\n    {\n        id: '4',\n        value: 24,\n        label: 'Soporte Técnico',\n        suffix: '/7'\n    }\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/data.ts\n"));

/***/ })

});