"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/sections/projects.tsx":
/*!**********************************************!*\
  !*** ./src/components/sections/projects.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProjectsSection: () => (/* binding */ ProjectsSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Cpu_Play_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Cpu,Play!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cpu.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Cpu_Play_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Cpu,Play!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Cpu_Play_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Cpu,Play!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Cpu_Play_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Cpu,Play!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Cpu_Play_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Cpu,Play!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Cpu_Play_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Cpu,Play!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Cpu_Play_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Cpu,Play!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _lib_data__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/data */ \"(app-pages-browser)/./src/lib/data.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ ProjectsSection auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst categories = [\n    'Todos',\n    'Alimentaria',\n    'Fluidos',\n    'Industrial'\n];\nfunction ProjectsSection() {\n    _s();\n    const [selectedCategory, setSelectedCategory] = react__WEBPACK_IMPORTED_MODULE_1__.useState('Todos');\n    const [currentProject, setCurrentProject] = react__WEBPACK_IMPORTED_MODULE_1__.useState(0);\n    const filteredProjects = selectedCategory === 'Todos' ? _lib_data__WEBPACK_IMPORTED_MODULE_2__.projects : _lib_data__WEBPACK_IMPORTED_MODULE_2__.projects.filter((project)=>project.category === selectedCategory);\n    const nextProject = ()=>{\n        setCurrentProject((prev)=>(prev + 1) % filteredProjects.length);\n    };\n    const prevProject = ()=>{\n        setCurrentProject((prev)=>(prev - 1 + filteredProjects.length) % filteredProjects.length);\n    };\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"ProjectsSection.useEffect\": ()=>{\n            setCurrentProject(0);\n        }\n    }[\"ProjectsSection.useEffect\"], [\n        selectedCategory\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"ProjectsSection.useEffect\": ()=>{\n            const timer = setInterval(nextProject, 5000);\n            return ({\n                \"ProjectsSection.useEffect\": ()=>clearInterval(timer)\n            })[\"ProjectsSection.useEffect\"];\n        }\n    }[\"ProjectsSection.useEffect\"], [\n        filteredProjects.length\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"proyectos\",\n        className: \"py-20 lg:py-32 bg-gray-50 dark:bg-gray-800 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 industrial-grid opacity-20\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        className: \"text-center mb-16 lg:mb-20\",\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                className: \"inline-flex items-center px-4 py-2 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 text-sm font-medium mb-6\",\n                                initial: {\n                                    opacity: 0,\n                                    scale: 0.8\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    scale: 1\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.2\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Cpu_Play_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Casos de \\xc9xito\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 dark:text-white mb-6\",\n                                children: [\n                                    \"Proyectos que \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"gradient-text\",\n                                        children: \"Transforman\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 27\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Industrias\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed\",\n                                children: \"Descubre c\\xf3mo hemos ayudado a empresas l\\xedderes a optimizar sus procesos y aumentar su productividad con soluciones innovadoras.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        className: \"flex flex-wrap justify-center gap-4 mb-12\",\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.6,\n                            delay: 0.3\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                onClick: ()=>setSelectedCategory(category),\n                                className: \"px-6 py-3 rounded-full font-medium transition-all duration-300 \".concat(selectedCategory === category ? 'bg-gradient-to-r from-blue-600 to-electric-500 text-white shadow-lg' : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 border border-gray-200 dark:border-gray-600'),\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                whileTap: {\n                                    scale: 0.95\n                                },\n                                children: category\n                            }, category, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n                                mode: \"wait\",\n                                children: filteredProjects.length > 0 && filteredProjects[currentProject] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: 300\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    exit: {\n                                        opacity: 0,\n                                        x: -300\n                                    },\n                                    transition: {\n                                        duration: 0.5\n                                    },\n                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                            className: \"relative group\",\n                                            whileHover: {\n                                                scale: 1.02\n                                            },\n                                            transition: {\n                                                duration: 0.3\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative aspect-[4/3] rounded-2xl overflow-hidden shadow-2xl\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            src: filteredProjects[currentProject].image,\n                                                            alt: filteredProjects[currentProject].title,\n                                                            fill: true,\n                                                            sizes: \"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\",\n                                                            className: \"object-cover transition-transform duration-500 group-hover:scale-110\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                            lineNumber: 128,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                            lineNumber: 135,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                                            className: \"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                                            whileHover: {\n                                                                scale: 1.1\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Cpu_Play_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"w-6 h-6 text-white ml-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                                    lineNumber: 143,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                                lineNumber: 142,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                            lineNumber: 138,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-4 left-4 px-3 py-1 bg-gradient-to-r from-blue-600 to-electric-500 text-white text-sm font-medium rounded-full\",\n                                                    children: filteredProjects[currentProject].category\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                                    initial: {\n                                                        opacity: 0,\n                                                        y: 20\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        y: 0\n                                                    },\n                                                    transition: {\n                                                        delay: 0.2\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4\",\n                                                            children: filteredProjects[currentProject].title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg text-gray-600 dark:text-gray-300 leading-relaxed mb-6\",\n                                                            children: filteredProjects[currentProject].description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                            lineNumber: 164,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                                    className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                                                    initial: {\n                                                        opacity: 0,\n                                                        y: 20\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        y: 0\n                                                    },\n                                                    transition: {\n                                                        delay: 0.3\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Cpu_Play_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"w-5 h-5 text-blue-600 dark:text-electric-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                                    lineNumber: 177,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600 dark:text-gray-300\",\n                                                                    children: filteredProjects[currentProject].year\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                                    lineNumber: 178,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                            lineNumber: 176,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        filteredProjects[currentProject].client && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Cpu_Play_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"w-5 h-5 text-blue-600 dark:text-electric-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                                    lineNumber: 184,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600 dark:text-gray-300\",\n                                                                    children: filteredProjects[currentProject].client\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                                    lineNumber: 185,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                            lineNumber: 183,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                                    initial: {\n                                                        opacity: 0,\n                                                        y: 20\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        y: 0\n                                                    },\n                                                    transition: {\n                                                        delay: 0.4\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-lg font-semibold text-gray-900 dark:text-white mb-3\",\n                                                            children: \"Tecnolog\\xedas Utilizadas:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap gap-2\",\n                                                            children: filteredProjects[currentProject].technologies.map((tech, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.span, {\n                                                                    className: \"px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 text-sm font-medium rounded-full\",\n                                                                    initial: {\n                                                                        opacity: 0,\n                                                                        scale: 0.8\n                                                                    },\n                                                                    animate: {\n                                                                        opacity: 1,\n                                                                        scale: 1\n                                                                    },\n                                                                    transition: {\n                                                                        delay: 0.5 + index * 0.1\n                                                                    },\n                                                                    whileHover: {\n                                                                        scale: 1.05\n                                                                    },\n                                                                    children: tech\n                                                                }, index, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                                    lineNumber: 203,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                            lineNumber: 201,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                                    initial: {\n                                                        opacity: 0,\n                                                        y: 20\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        y: 0\n                                                    },\n                                                    transition: {\n                                                        delay: 0.6\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, \"\".concat(selectedCategory, \"-\").concat(currentProject), true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, this),\n                            filteredProjects.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                        onClick: prevProject,\n                                        className: \"absolute left-4 top-1/2 -translate-y-1/2 w-12 h-12 bg-white dark:bg-gray-800 rounded-full shadow-lg flex items-center justify-center hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200 z-10\",\n                                        whileHover: {\n                                            scale: 1.1\n                                        },\n                                        whileTap: {\n                                            scale: 0.9\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Cpu_Play_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"w-6 h-6 text-gray-600 dark:text-gray-300\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                        onClick: nextProject,\n                                        className: \"absolute right-4 top-1/2 -translate-y-1/2 w-12 h-12 bg-white dark:bg-gray-800 rounded-full shadow-lg flex items-center justify-center hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200 z-10\",\n                                        whileHover: {\n                                            scale: 1.1\n                                        },\n                                        whileTap: {\n                                            scale: 0.9\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Cpu_Play_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-6 h-6 text-gray-600 dark:text-gray-300\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, this),\n                    filteredProjects.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        className: \"flex justify-center space-x-2 mt-8\",\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        transition: {\n                            delay: 0.8\n                        },\n                        children: filteredProjects.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                onClick: ()=>setCurrentProject(index),\n                                className: \"w-3 h-3 rounded-full transition-all duration-300 \".concat(index === currentProject ? 'bg-gradient-to-r from-blue-600 to-electric-500 scale-125' : 'bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500'),\n                                whileHover: {\n                                    scale: 1.2\n                                },\n                                whileTap: {\n                                    scale: 0.9\n                                }\n                            }, index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                        lineNumber: 256,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        className: \"text-center mt-16 lg:mt-20\",\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8,\n                            delay: 0.4\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-gray-600 dark:text-gray-300 mb-8\",\n                                children: \"\\xbfListo para ser nuestro pr\\xf3ximo caso de \\xe9xito?\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                size: \"lg\",\n                                className: \"bg-gradient-to-r from-blue-600 to-electric-500 hover:from-blue-700 hover:to-electric-600 text-white font-semibold px-8 py-4 rounded-full shadow-xl hover:shadow-2xl transition-all duration-300\",\n                                onClick: ()=>{\n                                    const contactSection = document.getElementById('contacto');\n                                    contactSection === null || contactSection === void 0 ? void 0 : contactSection.scrollIntoView({\n                                        behavior: 'smooth'\n                                    });\n                                },\n                                children: [\n                                    \"Iniciar Mi Proyecto\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Cpu_Play_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"ml-2 w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, this);\n}\n_s(ProjectsSection, \"OGC6bCZmR6xCKp4smftKUdaqSE4=\");\n_c = ProjectsSection;\nvar _c;\n$RefreshReg$(_c, \"ProjectsSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/projects.tsx\n"));

/***/ })

});