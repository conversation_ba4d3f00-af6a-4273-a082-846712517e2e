"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/sections/about.tsx":
/*!*******************************************!*\
  !*** ./src/components/sections/about.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AboutSection: () => (/* binding */ AboutSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Eye_Lightbulb_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Eye,Lightbulb,Shield,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Eye_Lightbulb_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Eye,Lightbulb,Shield,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Eye_Lightbulb_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Eye,Lightbulb,Shield,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Eye_Lightbulb_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Eye,Lightbulb,Shield,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Eye_Lightbulb_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Eye,Lightbulb,Shield,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Eye_Lightbulb_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Eye,Lightbulb,Shield,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Eye_Lightbulb_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Eye,Lightbulb,Shield,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _lib_data__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/data */ \"(app-pages-browser)/./src/lib/data.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ AboutSection auto */ \n\n\n\n\n\n\nconst values = [\n    {\n        icon: _barrel_optimize_names_ArrowRight_Eye_Lightbulb_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: 'Innovación',\n        description: 'Buscamos constantemente nuevas tecnologías y metodologías para ofrecer soluciones de vanguardia.'\n    },\n    {\n        icon: _barrel_optimize_names_ArrowRight_Eye_Lightbulb_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        title: 'Confiabilidad',\n        description: 'Construimos relaciones duraderas basadas en la transparencia, calidad y cumplimiento de compromisos.'\n    },\n    {\n        icon: _barrel_optimize_names_ArrowRight_Eye_Lightbulb_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        title: 'Colaboración',\n        description: 'Trabajamos como socios estratégicos, integrando nuestro equipo con el de nuestros clientes.'\n    },\n    {\n        icon: _barrel_optimize_names_ArrowRight_Eye_Lightbulb_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        title: 'Excelencia',\n        description: 'Nos comprometemos con los más altos estándares de calidad en cada proyecto que emprendemos.'\n    }\n];\nconst milestones = [\n    {\n        year: '2010',\n        title: 'Fundación',\n        description: 'Primeros trabajos'\n    },\n    {\n        year: '2013',\n        title: 'Expansión',\n        description: 'Primeros proyectos internacionales'\n    },\n    {\n        year: '2016',\n        title: 'Certificación ISO',\n        description: 'Obtención de certificaciones de calidad'\n    },\n    {\n        year: '2019',\n        title: 'Innovación',\n        description: 'Incorporación de IA y IoT'\n    },\n    {\n        year: '2022',\n        title: 'Liderazgo',\n        description: 'Reconocimiento como empresa líder'\n    },\n    {\n        year: '2024',\n        title: 'Futuro',\n        description: 'Nuevas tecnologías y mercados'\n    }\n];\nfunction AboutSection() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"nosotros\",\n        className: \"py-20 lg:py-32 bg-white dark:bg-gray-900 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 industrial-grid opacity-10\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-20 right-10 w-72 h-72 bg-electric-500/5 rounded-full blur-3xl\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-20 left-10 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        className: \"text-center mb-16 lg:mb-20\",\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                className: \"inline-flex items-center px-4 py-2 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 text-sm font-medium mb-6\",\n                                initial: {\n                                    opacity: 0,\n                                    scale: 0.8\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    scale: 1\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.2\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Eye_Lightbulb_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Conoce a Servindex\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 dark:text-white mb-6\",\n                                children: [\n                                    \"M\\xe1s de 10 A\\xf1os \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"gradient-text\",\n                                        children: \"Transformando\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 28\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"la Industria\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed\",\n                                children: \"Somos pioneros en automatizaci\\xf3n industrial en Extremadura, con un equipo apasionado por la innovaci\\xf3n y comprometido con el \\xe9xito de nuestros clientes.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center mb-20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: -50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-3xl font-bold text-gray-900 dark:text-white mb-6\",\n                                        children: \"Nuestra Historia\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4 text-lg text-gray-600 dark:text-gray-300 leading-relaxed\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Servindex nace en 2025 en Extremadura, fruto del esfuerzo de dos t\\xe9cnicos que iniciaron su camino en la automatizaci\\xf3n industrial hace m\\xe1s de diez a\\xf1os.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 11\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"A lo largo de nuestro recorrido hemos participado en proyectos de alto nivel, integrando soluciones que optimizan procesos y mejoran la productividad de nuestros clientes.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 11\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Hoy seguimos creciendo, llevando nuestra experiencia a cada instalaci\\xf3n y ofreciendo un servicio cercano, profesional y adaptado a las necesidades de cada empresa.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 11\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                className: \"relative\",\n                                initial: {\n                                    opacity: 0,\n                                    x: 50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: milestones.map((milestone, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                            className: \"flex items-start space-x-4\",\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: index * 0.1\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 w-16 h-16 bg-gradient-to-br from-blue-600 to-electric-500 rounded-full flex items-center justify-center text-white font-bold\",\n                                                    children: milestone.year.slice(-2)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                                            children: milestone.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                                            lineNumber: 145,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 dark:text-gray-300\",\n                                                            children: milestone.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                                            lineNumber: 148,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 mb-20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                className: \"bg-gradient-to-br from-blue-50 to-electric-50 dark:from-blue-900/20 dark:to-electric-900/20 rounded-2xl p-8\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                whileHover: {\n                                    scale: 1.02\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gradient-to-br from-blue-600 to-electric-500 rounded-xl flex items-center justify-center mr-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Eye_Lightbulb_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-6 h-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                                children: \"Nuestra Misi\\xf3n\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-gray-600 dark:text-gray-300 leading-relaxed\",\n                                        children: \"Transformar la industria mediante soluciones de automatizaci\\xf3n innovadoras, eficientes y sostenibles que impulsen el crecimiento y la competitividad de nuestros clientes.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                className: \"bg-gradient-to-br from-electric-50 to-blue-50 dark:from-electric-900/20 dark:to-blue-900/20 rounded-2xl p-8\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.2\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                whileHover: {\n                                    scale: 1.02\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gradient-to-br from-electric-500 to-blue-600 rounded-xl flex items-center justify-center mr-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Eye_Lightbulb_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-6 h-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                                children: \"Nuestra Visi\\xf3n\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-gray-600 dark:text-gray-300 leading-relaxed\",\n                                        children: \"Ser la empresa de referencia en automatizaci\\xf3n industrial en el sur de Europa, reconocida por nuestra excelencia t\\xe9cnica, innovaci\\xf3n constante y compromiso con el \\xe9xito de nuestros clientes.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        className: \"mb-20\",\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-3xl font-bold text-gray-900 dark:text-white mb-4\",\n                                        children: \"Nuestros Valores\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-gray-600 dark:text-gray-300\",\n                                        children: \"Los principios que gu\\xedan cada decisi\\xf3n y acci\\xf3n en Servindex\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                                children: values.map((value, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                        className: \"text-center group\",\n                                        initial: {\n                                            opacity: 0,\n                                            y: 30\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: index * 0.1\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        whileHover: {\n                                            y: -10\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                                className: \"w-16 h-16 bg-gradient-to-br from-blue-600 to-electric-500 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300\",\n                                                whileHover: {\n                                                    rotate: 360\n                                                },\n                                                transition: {\n                                                    duration: 0.6\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(value.icon, {\n                                                    className: \"w-8 h-8 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-xl font-bold text-gray-900 dark:text-white mb-3\",\n                                                children: value.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 dark:text-gray-300 leading-relaxed\",\n                                                children: value.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-3xl font-bold text-gray-900 dark:text-white mb-4\",\n                                        children: \"Nuestro Equipo\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-gray-600 dark:text-gray-300\",\n                                        children: \"Profesionales apasionados por la innovaci\\xf3n y la excelencia\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-8 mb-12\",\n                                children: _lib_data__WEBPACK_IMPORTED_MODULE_2__.teamMembers.map((member, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                        className: \"group\",\n                                        initial: {\n                                            opacity: 0,\n                                            y: 30\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: index * 0.1\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        whileHover: {\n                                            y: -10\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-electric-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative mb-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-24 h-24 mx-auto rounded-full overflow-hidden\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            src: member.image,\n                                                            alt: member.name,\n                                                            width: 96,\n                                                            height: 96,\n                                                            className: \"w-full h-full object-cover grayscale group-hover:grayscale-0 transition-all duration-300\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-xl font-bold text-gray-900 dark:text-white mb-2\",\n                                                            children: member.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                                            lineNumber: 293,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-blue-600 dark:text-electric-400 font-medium mb-3\",\n                                                            children: member.position\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                                            lineNumber: 296,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 dark:text-gray-300 text-sm leading-relaxed\",\n                                                            children: member.bio\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                                            lineNumber: 299,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, member.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-gray-600 dark:text-gray-300 mb-8\",\n                                        children: \"\\xbfQuieres formar parte de nuestro equipo?\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        size: \"lg\",\n                                        className: \"bg-gradient-to-r from-blue-600 to-electric-500 hover:from-blue-700 hover:to-electric-600 text-white font-semibold px-8 py-4 rounded-full shadow-xl hover:shadow-2xl transition-all duration-300\",\n                                        children: [\n                                            \"Ver Oportunidades\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Eye_Lightbulb_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"ml-2 w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\n_c = AboutSection;\nvar _c;\n$RefreshReg$(_c, \"AboutSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/about.tsx\n"));

/***/ })

});