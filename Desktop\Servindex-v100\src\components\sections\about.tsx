'use client';

import * as React from 'react';
import { motion } from 'framer-motion';
import { 
  Target, 
  Eye, 
  Heart, 
  Users, 
  Award,
  Lightbulb,
  Shield,
  Zap,
  ArrowRight
} from 'lucide-react';
import { teamMembers } from '@/lib/data';
import { Button } from '@/components/ui/button';
import Image from 'next/image';

const values = [
  {
    icon: Lightbulb,
    title: 'Innovación',
    description: 'Buscamos constantemente nuevas tecnologías y metodologías para ofrecer soluciones de vanguardia.',
  },
  {
    icon: Shield,
    title: 'Confiabilidad',
    description: 'Construimos relaciones duraderas basadas en la transparencia, calidad y cumplimiento de compromisos.',
  },
  {
    icon: Users,
    title: 'Colaboración',
    description: 'Trabajamos como socios estratégicos, integrando nuestro equipo con el de nuestros clientes.',
  },
  {
    icon: Zap,
    title: 'Excelencia',
    description: 'Nos comprometemos con los más altos estándares de calidad en cada proyecto que emprendemos.',
  },
];

const milestones = [
  { year: '2015', title: 'Inicio', description: 'Primeros trabajos' },
  { year: '2017', title: 'Crecimiento', description: 'Crecimiento y formación' },
  { year: '2021', title: 'Especialización', description: 'Automatización y especialización' },
  { year: '2024', title: 'Liderazgo', description: 'Proyectos de alto nivel' },
  { year: '2025', title: 'Fundación', description: 'Fundación de Servindex' },
];

export function AboutSection() {
  return (
    <section id="nosotros" className="py-20 lg:py-32 bg-white dark:bg-gray-900 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 industrial-grid opacity-10" />
      <div className="absolute top-20 right-10 w-72 h-72 bg-electric-500/5 rounded-full blur-3xl" />
      <div className="absolute bottom-20 left-10 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl" />
      
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          className="text-center mb-16 lg:mb-20"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <motion.div
            className="inline-flex items-center px-4 py-2 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 text-sm font-medium mb-6"
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <Users className="w-4 h-4 mr-2" />
            Conoce a Servindex
          </motion.div>
          
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 dark:text-white mb-6">
            Más de 10 Años <span className="gradient-text">Transformando</span>
            <br />
            la Industria
          </h2>
          
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Somos pioneros en automatización industrial en Extremadura, con un equipo apasionado 
            por la innovación y comprometido con el éxito de nuestros clientes.
          </p>
        </motion.div>

        {/* Story Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center mb-20">
          {/* Story Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h3 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">
              Nuestra Historia
            </h3>
            <div className="space-y-4 text-lg text-gray-600 dark:text-gray-300 leading-relaxed">
          <p>
            Servindex nace en 2025 en Extremadura, fruto del esfuerzo de dos técnicos 
            que iniciaron su camino en la automatización industrial hace más de diez años.
          </p>
          <p>
            A lo largo de nuestro recorrido hemos participado en proyectos de alto nivel, 
            integrando soluciones que optimizan procesos y mejoran la productividad de 
            nuestros clientes.
          </p>
          <p>
            Hoy seguimos creciendo, llevando nuestra experiencia a cada instalación y 
            ofreciendo un servicio cercano, profesional y adaptado a las necesidades de 
            cada empresa.
          </p>

            </div>
          </motion.div>

          {/* Timeline */}
          <motion.div
            className="relative"
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="space-y-6">
              {milestones.map((milestone, index) => (
                <motion.div
                  key={index}
                  className="flex items-start space-x-4"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <div className="flex-shrink-0 w-16 h-16 bg-gradient-to-br from-blue-600 to-electric-500 rounded-full flex items-center justify-center text-white font-bold">
                    {milestone.year.slice(-2)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
                      {milestone.title}
                    </h4>
                    <p className="text-gray-600 dark:text-gray-300">
                      {milestone.description}
                    </p>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>

        {/* Mission & Vision */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-20">
          <motion.div
            className="bg-gradient-to-br from-blue-50 to-electric-50 dark:from-blue-900/20 dark:to-electric-900/20 rounded-2xl p-8"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            whileHover={{ scale: 1.02 }}
          >
            <div className="flex items-center mb-6">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-600 to-electric-500 rounded-xl flex items-center justify-center mr-4">
                <Target className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                Nuestra Misión
              </h3>
            </div>
            <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed">
              Transformar la industria mediante soluciones de automatización innovadoras, 
              eficientes y sostenibles que impulsen el crecimiento y la competitividad 
              de nuestros clientes.
            </p>
          </motion.div>

          <motion.div
            className="bg-gradient-to-br from-electric-50 to-blue-50 dark:from-electric-900/20 dark:to-blue-900/20 rounded-2xl p-8"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            whileHover={{ scale: 1.02 }}
          >
            <div className="flex items-center mb-6">
              <div className="w-12 h-12 bg-gradient-to-br from-electric-500 to-blue-600 rounded-xl flex items-center justify-center mr-4">
                <Eye className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                Nuestra Visión
              </h3>
            </div>
            <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed">
              Ser la empresa de referencia en automatización industrial en el sur de Europa, 
              reconocida por nuestra excelencia técnica, innovación constante y 
              compromiso con el éxito de nuestros clientes.
            </p>
          </motion.div>
        </div>

        {/* Values */}
        <motion.div
          className="mb-20"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Nuestros Valores
            </h3>
            <p className="text-lg text-gray-600 dark:text-gray-300">
              Los principios que guían cada decisión y acción en Servindex
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <motion.div
                key={index}
                className="text-center group"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -10 }}
              >
                <motion.div
                  className="w-16 h-16 bg-gradient-to-br from-blue-600 to-electric-500 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300"
                  whileHover={{ rotate: 360 }}
                  transition={{ duration: 0.6 }}
                >
                  <value.icon className="w-8 h-8 text-white" />
                </motion.div>
                <h4 className="text-xl font-bold text-gray-900 dark:text-white mb-3">
                  {value.title}
                </h4>
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                  {value.description}
                </p>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Team Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Nuestro Equipo
            </h3>
            <p className="text-lg text-gray-600 dark:text-gray-300">
              Profesionales apasionados por la innovación y la excelencia
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            {teamMembers.map((member, index) => (
              <motion.div
                key={member.id}
                className="group"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -10 }}
              >
                <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-electric-400">
                  <div className="relative mb-6">
                    <div className="w-24 h-24 mx-auto rounded-full overflow-hidden">
                      <Image
                        src={member.image}
                        alt={member.name}
                        width={96}
                        height={96}
                        className="w-full h-full object-cover grayscale group-hover:grayscale-0 transition-all duration-300"
                      />
                    </div>
                  </div>
                  <div className="text-center">
                    <h4 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                      {member.name}
                    </h4>
                    <p className="text-blue-600 dark:text-electric-400 font-medium mb-3">
                      {member.position}
                    </p>
                    <p className="text-gray-600 dark:text-gray-300 text-sm leading-relaxed">
                      {member.bio}
                    </p>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* CTA */}
          <div className="text-center">
            <p className="text-lg text-gray-600 dark:text-gray-300 mb-8">
              ¿Quieres formar parte de nuestro equipo?
            </p>
            <Button
              size="lg"
              className="bg-gradient-to-r from-blue-600 to-electric-500 hover:from-blue-700 hover:to-electric-600 text-white font-semibold px-8 py-4 rounded-full shadow-xl hover:shadow-2xl transition-all duration-300"
             onClick={() => {
              const contactSection = document.getElementById('contacto');
              contactSection?.scrollIntoView({ behavior: 'smooth' });
            }}
            >
              Ver Oportunidades
              <ArrowRight className="ml-2 w-5 h-5" />
            </Button>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
