"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/sections/services.tsx":
/*!**********************************************!*\
  !*** ./src/components/sections/services.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ServicesSection: () => (/* binding */ ServicesSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Bot_CheckCircle_Code_Settings_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Bot,CheckCircle,Code,Settings,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Bot_CheckCircle_Code_Settings_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Bot,CheckCircle,Code,Settings,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Bot_CheckCircle_Code_Settings_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Bot,CheckCircle,Code,Settings,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Bot_CheckCircle_Code_Settings_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Bot,CheckCircle,Code,Settings,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Bot_CheckCircle_Code_Settings_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Bot,CheckCircle,Code,Settings,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Bot_CheckCircle_Code_Settings_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Bot,CheckCircle,Code,Settings,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Bot_CheckCircle_Code_Settings_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Bot,CheckCircle,Code,Settings,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Bot_CheckCircle_Code_Settings_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Bot,CheckCircle,Code,Settings,Users,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _lib_data__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/data */ \"(app-pages-browser)/./src/lib/data.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ ServicesSection auto */ \n\n\n\n\n\nconst iconMap = {\n    Settings: _barrel_optimize_names_ArrowRight_Bot_CheckCircle_Code_Settings_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n    Bot: _barrel_optimize_names_ArrowRight_Bot_CheckCircle_Code_Settings_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n    Code: _barrel_optimize_names_ArrowRight_Bot_CheckCircle_Code_Settings_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n    Zap: _barrel_optimize_names_ArrowRight_Bot_CheckCircle_Code_Settings_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n    Users: _barrel_optimize_names_ArrowRight_Bot_CheckCircle_Code_Settings_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n    Wrench: _barrel_optimize_names_ArrowRight_Bot_CheckCircle_Code_Settings_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n};\nconst containerVariants = {\n    hidden: {\n        opacity: 0\n    },\n    visible: {\n        opacity: 1,\n        transition: {\n            staggerChildren: 0.2\n        }\n    }\n};\nconst cardVariants = {\n    hidden: {\n        opacity: 0,\n        y: 50\n    },\n    visible: {\n        opacity: 1,\n        y: 0,\n        transition: {\n            duration: 0.6,\n            ease: 'easeOut'\n        }\n    }\n};\nfunction ServicesSection() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"servicios\",\n        className: \"py-20 lg:py-32 bg-gray-50 dark:bg-gray-900 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 industrial-grid opacity-20\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\services.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                        className: \"text-center mb-16 lg:mb-20\",\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                className: \"inline-flex items-center px-4 py-2 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 text-sm font-medium mb-6\",\n                                initial: {\n                                    opacity: 0,\n                                    scale: 0.8\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    scale: 1\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.2\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Bot_CheckCircle_Code_Settings_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\services.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Nuestros Servicios\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\services.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 dark:text-white mb-6\",\n                                children: [\n                                    \"Soluciones \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"gradient-text\",\n                                        children: \"Industriales\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\services.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 24\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\services.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"de Vanguardia\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\services.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed\",\n                                children: \"Transformamos procesos industriales con tecnolog\\xeda avanzada, automatizaci\\xf3n inteligente y soluciones personalizadas para cada sector.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\services.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\services.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-10\",\n                        variants: containerVariants,\n                        initial: \"hidden\",\n                        whileInView: \"visible\",\n                        viewport: {\n                            once: true\n                        },\n                        children: _lib_data__WEBPACK_IMPORTED_MODULE_2__.services.map((service, index)=>{\n                            const IconComponent = iconMap[service.icon];\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                variants: cardVariants,\n                                whileHover: {\n                                    y: -10,\n                                    scale: 1.02\n                                },\n                                className: \"group\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-electric-400 relative overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-br from-blue-50/50 to-electric-50/50 dark:from-blue-900/20 dark:to-electric-900/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\services.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative z-10\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                                    className: \"w-16 h-16 bg-gradient-to-br from-blue-600 to-electric-500 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300\",\n                                                    whileHover: {\n                                                        rotate: 360\n                                                    },\n                                                    transition: {\n                                                        duration: 0.6\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                        className: \"w-8 h-8 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\services.tsx\",\n                                                        lineNumber: 117,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\services.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-2xl font-bold text-gray-900 dark:text-white mb-4 group-hover:text-blue-600 dark:group-hover:text-electric-400 transition-colors duration-300\",\n                                                    children: service.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\services.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 dark:text-gray-300 mb-6 leading-relaxed\",\n                                                    children: service.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\services.tsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-3 mb-8\",\n                                                    children: service.features.map((feature, featureIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.li, {\n                                                            className: \"flex items-center text-sm text-gray-600 dark:text-gray-300\",\n                                                            initial: {\n                                                                opacity: 0,\n                                                                x: -20\n                                                            },\n                                                            whileInView: {\n                                                                opacity: 1,\n                                                                x: 0\n                                                            },\n                                                            transition: {\n                                                                delay: index * 0.1 + featureIndex * 0.1\n                                                            },\n                                                            viewport: {\n                                                                once: true\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Bot_CheckCircle_Code_Settings_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-green-500 mr-3 flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\services.tsx\",\n                                                                    lineNumber: 141,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                feature\n                                                            ]\n                                                        }, featureIndex, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\services.tsx\",\n                                                            lineNumber: 133,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\services.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"outline\",\n                                                    className: \"w-full group-hover:bg-blue-600 group-hover:text-white group-hover:border-blue-600 dark:group-hover:bg-electric-500 dark:group-hover:border-electric-500 transition-all duration-300\",\n                                                    children: [\n                                                        \"M\\xe1s Informaci\\xf3n\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Bot_CheckCircle_Code_Settings_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\services.tsx\",\n                                                            lineNumber: 153,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\services.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\services.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-4 right-4 w-20 h-20 bg-gradient-to-br from-blue-100 to-electric-100 dark:from-blue-900/30 dark:to-electric-900/30 rounded-full opacity-20 group-hover:opacity-40 transition-opacity duration-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\services.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -bottom-4 -right-4 w-16 h-16 bg-gradient-to-br from-electric-200 to-blue-200 dark:from-electric-800/30 dark:to-blue-800/30 rounded-full opacity-20 group-hover:opacity-40 transition-opacity duration-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\services.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\services.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 17\n                                }, this)\n                            }, service.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\services.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\services.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                        className: \"text-center mt-16 lg:mt-20\",\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8,\n                            delay: 0.4\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-gray-600 dark:text-gray-300 mb-8\",\n                                children: \"\\xbfNo encuentras lo que buscas? Ofrecemos soluciones personalizadas para cada industria.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\services.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                size: \"lg\",\n                                className: \"bg-gradient-to-r from-blue-600 to-electric-500 hover:from-blue-700 hover:to-electric-600 text-white font-semibold px-8 py-4 rounded-full shadow-xl hover:shadow-2xl transition-all duration-300\",\n                                onClick: ()=>{\n                                    const contactSection = document.getElementById('contacto');\n                                    contactSection === null || contactSection === void 0 ? void 0 : contactSection.scrollIntoView({\n                                        behavior: 'smooth'\n                                    });\n                                },\n                                children: [\n                                    \"Consulta Personalizada\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Bot_CheckCircle_Code_Settings_Users_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"ml-2 w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\services.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\services.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\services.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\services.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\services.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n_c = ServicesSection;\nvar _c;\n$RefreshReg$(_c, \"ServicesSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/services.tsx\n"));

/***/ })

});