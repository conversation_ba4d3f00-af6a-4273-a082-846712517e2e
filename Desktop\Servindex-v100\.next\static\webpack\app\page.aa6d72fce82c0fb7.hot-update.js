"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/data.ts":
/*!*************************!*\
  !*** ./src/lib/data.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   blogPosts: () => (/* binding */ blogPosts),\n/* harmony export */   projects: () => (/* binding */ projects),\n/* harmony export */   services: () => (/* binding */ services),\n/* harmony export */   statistics: () => (/* binding */ statistics),\n/* harmony export */   teamMembers: () => (/* binding */ teamMembers)\n/* harmony export */ });\nconst services = [\n    {\n        id: '1',\n        title: 'Automatización Industrial',\n        description: 'Sistemas de automatización completos para optimizar procesos industriales y aumentar la eficiencia operativa.',\n        icon: 'Settings',\n        features: [\n            'Diseño de sistemas automatizados',\n            'Integración de sensores y actuadores',\n            'Optimización de procesos',\n            'Reducción de costos operativos'\n        ]\n    },\n    {\n        id: '2',\n        title: 'Robótica Industrial',\n        description: 'Implementación de soluciones robóticas avanzadas para manufactura y manipulación de materiales.',\n        icon: 'Bot',\n        features: [\n            'Robots colaborativos (cobots)',\n            'Sistemas de visión artificial',\n            'Programación de trayectorias',\n            'Mantenimiento predictivo'\n        ]\n    },\n    {\n        id: '3',\n        title: 'Programación PLC/SCADA',\n        description: 'Desarrollo y programación de sistemas de control distribuido para monitoreo y control de procesos industriales.',\n        icon: 'Code',\n        features: [\n            'Programación de PLCs',\n            'Desarrollo de interfaces SCADA',\n            'Sistemas de alarmas',\n            'Históricos y reportes'\n        ]\n    },\n    {\n        id: '4',\n        title: 'Cuadros Eléctricos',\n        description: 'Diseño, fabricación e instalación de cuadros eléctricos industriales con los más altos estándares de calidad.',\n        icon: 'Zap',\n        features: [\n            'Diseño según normativas',\n            'Fabricación personalizada',\n            'Instalación y puesta en marcha',\n            'Certificaciones de calidad'\n        ]\n    },\n    {\n        id: '5',\n        title: 'Consultoría Técnica',\n        description: 'Asesoramiento especializado en ingeniería industrial, optimización de procesos y transformación digital.',\n        icon: 'Users',\n        features: [\n            'Auditorías técnicas',\n            'Planes de modernización',\n            'Análisis de eficiencia',\n            'Formación especializada'\n        ]\n    },\n    {\n        id: '6',\n        title: 'Mantenimiento Industrial',\n        description: 'Servicios integrales de mantenimiento preventivo, predictivo y correctivo para equipos industriales.',\n        icon: 'Wrench',\n        features: [\n            'Mantenimiento preventivo',\n            'Diagnóstico predictivo',\n            'Reparaciones especializadas',\n            'Gestión de repuestos'\n        ]\n    }\n];\nconst projects = [\n    {\n        id: '1',\n        title: 'Automatización de Línea de Tomate Concentrado',\n        description: 'Optimización completa de la línea de producción de tomate concentrado mediante control automatizado y monitoreo en tiempo real.',\n        image: '/imagenes/1.jpg',\n        category: 'Alimentaria',\n        technologies: [\n            'Siemens Step7',\n            'Profibus',\n            'Intouch',\n            'SCADA',\n            'PLC'\n        ],\n        year: 2025,\n        client: 'Conesa Group'\n    },\n    {\n        id: '2',\n        title: 'Producción Automatizada de Tomate Frito',\n        description: 'Automatización integral del proceso de tomate frito, desde la cocción hasta el envasado final, garantizando calidad constante.',\n        image: '/imagenes/7.jpg',\n        category: 'Alimentaria',\n        technologies: [\n            'CX-Programmer',\n            'CX-Supervisor',\n            'Weidmuller',\n            'Instrumentación'\n        ],\n        year: 2024,\n        client: 'Tomcoex'\n    },\n    {\n        id: '3',\n        title: 'Sistema de Filtrado de Arena Industrial',\n        description: 'Automatización de filtrado industrial con control de presión y ciclos de retrolavado automáticos para mayor eficiencia.',\n        image: '/imagenes/6.jpg',\n        category: 'Fluidos',\n        technologies: [\n            'PLC Omron',\n            'CX-Supervisor',\n            'HMI Proface',\n            'Válvulas Automáticas'\n        ],\n        year: 2025,\n        client: 'Agraz'\n    },\n    {\n        id: '4',\n        title: 'Línea Automatizada de Pelado de Almendras',\n        description: 'Control automático de rodillos y detección de obstrucciones para un pelado seguro y eficiente.',\n        image: '/imagenes/5.jpg',\n        category: 'Alimentaria',\n        technologies: [\n            'PLC Omron',\n            'HMI Proface',\n            'Variadores de Frecuencia'\n        ],\n        year: 2024,\n        client: 'Acopaex'\n    },\n    {\n        id: '5',\n        title: 'Producción Automatizada de Sustratos',\n        description: 'Línea de mezclado y envasado de sustratos con control de pesaje, humedad y flujo de material optimizado.',\n        image: '/imagenes/3.jpg',\n        category: 'Industrial',\n        technologies: [\n            'Siemens S7-1200',\n            'Profinet',\n            'Arrancador Schneider',\n            'Variadores'\n        ],\n        year: 2023,\n        client: 'Sustratos de Extremadura'\n    },\n    {\n        id: '6',\n        title: 'Pasadoras y Refinadoras de Tomate Automatizadas',\n        description: 'Control preciso de caudal y proceso en pasadoras y refinadoras para garantizar la calidad del producto final.',\n        image: '/imagenes/8.jpg',\n        category: 'Alimentaria',\n        technologies: [\n            'PLC Omron',\n            'HMI Proface',\n            'Variadores de Frecuencia'\n        ],\n        year: 2024,\n        client: 'Conesa Group'\n    },\n    {\n        id: '7',\n        title: 'Sistema de Extracción de gases',\n        description: 'Automatización de ventilación industrial con control de caudal, presión y alarmas de seguridad en tiempo real.',\n        image: '/imagenes/4.jpg',\n        category: 'Fluidos',\n        technologies: [\n            'PLC Simenes 1500',\n            'Redundancia',\n            'HMI Unified',\n            'Valvulas Automáticas'\n        ],\n        year: 2024,\n        client: 'Diamond Foundry'\n    }\n];\nconst blogPosts = [\n    {\n        id: '1',\n        title: 'El Futuro de la Automatización Industrial',\n        excerpt: 'Exploramos las tendencias emergentes en automatización y cómo están transformando la industria.',\n        content: '',\n        image: '/post/1.jpg',\n        author: 'Equipo Servindex',\n        publishedAt: '2025-01-15',\n        category: 'Automatización',\n        tags: [\n            'Industria 4.0',\n            'IoT',\n            'Automatización'\n        ],\n        readTime: 5\n    },\n    {\n        id: '2',\n        title: 'Mantenimiento Predictivo con IA',\n        excerpt: 'Cómo la inteligencia artificial está revolucionando el mantenimiento industrial.',\n        content: '',\n        image: '/post/2.jpg',\n        author: 'Equipo Servindex',\n        publishedAt: '2024-01-10',\n        category: 'Mantenimiento',\n        tags: [\n            'IA',\n            'Mantenimiento',\n            'Predictivo'\n        ],\n        readTime: 7\n    },\n    {\n        id: '3',\n        title: 'Robots Colaborativos en la Industria',\n        excerpt: 'Los cobots están cambiando la forma de trabajar en fábricas y talleres.',\n        content: '',\n        image: '/post/3.jpg',\n        author: 'Equipo Servindex',\n        publishedAt: '2025-02-01',\n        category: 'Robótica',\n        tags: [\n            'Cobots',\n            'Automatización',\n            'Industria 4.0'\n        ],\n        readTime: 6\n    },\n    {\n        id: '4',\n        title: 'Sensores Inteligentes y Control en Tiempo Real',\n        excerpt: 'La importancia de la sensórica avanzada para mejorar la eficiencia de las líneas de producción.',\n        content: '',\n        image: '/post/4.jpg',\n        author: 'Equipo Servindex',\n        publishedAt: '2025-02-05',\n        category: 'Automatización',\n        tags: [\n            'Sensores',\n            'Control Industrial',\n            'Eficiencia'\n        ],\n        readTime: 5\n    },\n    {\n        id: '5',\n        title: 'Transformación Digital en Fábricas',\n        excerpt: 'Cómo digitalizar procesos industriales y aumentar la competitividad.',\n        content: '',\n        image: '/post/5.jpg',\n        author: 'Equipo Servindex',\n        publishedAt: '2024-03-17',\n        category: 'Transformación Digital',\n        tags: [\n            'Digitalización',\n            'IoT',\n            'Industria'\n        ],\n        readTime: 6\n    },\n    {\n        id: '6',\n        title: 'SCADA y Monitoreo Remoto',\n        excerpt: 'Supervisión de procesos industriales desde cualquier lugar con sistemas SCADA modernos.',\n        content: '',\n        image: '/post/6.jpg',\n        author: 'Equipo Servindex',\n        publishedAt: '2025-04-15',\n        category: 'Control Industrial',\n        tags: [\n            'SCADA',\n            'Monitoreo',\n            'Automatización'\n        ],\n        readTime: 5\n    },\n    {\n        id: '7',\n        title: 'Ciberseguridad en Sistemas Industriales',\n        excerpt: 'Proteger instalaciones industriales frente a amenazas digitales es clave en la Industria 4.0.',\n        content: '',\n        image: '/post/7.jpg',\n        author: 'Equipo Servindex',\n        publishedAt: '2025-07-20',\n        category: 'Seguridad Industrial',\n        tags: [\n            'Ciberseguridad',\n            'Industria 4.0',\n            'Protección'\n        ],\n        readTime: 8\n    },\n    {\n        id: '8',\n        title: 'Eficiencia Energética en la Industria',\n        excerpt: 'Estrategias y tecnologías para reducir el consumo energético de las fábricas.',\n        content: '',\n        image: '/post/8.jpg',\n        author: 'Equipo Servindex',\n        publishedAt: '2025-08-01',\n        category: 'Energía',\n        tags: [\n            'Eficiencia',\n            'Ahorro Energético',\n            'Industria'\n        ],\n        readTime: 6\n    },\n    {\n        id: '9',\n        title: 'Automatización de Líneas de Envasado',\n        excerpt: 'Optimizando la producción con sistemas automáticos en líneas de envasado.',\n        content: '',\n        image: '/post/9.jpg',\n        author: 'Equipo Servindex',\n        publishedAt: '2024-06-05',\n        category: 'Automatización',\n        tags: [\n            'Envasado',\n            'Automatización',\n            'Producción'\n        ],\n        readTime: 5\n    },\n    {\n        id: '10',\n        title: 'Gemelos Digitales en la Industria',\n        excerpt: 'Simulación avanzada de procesos industriales para reducir riesgos y costos.',\n        content: '',\n        image: '/post/10.jpg',\n        author: 'Equipo Servindex',\n        publishedAt: '2024-09-10',\n        category: 'Innovación',\n        tags: [\n            'Gemelo Digital',\n            'Industria 4.0',\n            'Simulación'\n        ],\n        readTime: 7\n    }\n];\nconst teamMembers = [\n    {\n        id: '1',\n        name: 'Pablo Rebollo',\n        position: 'Director Técnico',\n        image: '/imagenes/pablo.png',\n        bio: 'Técnico polivalente en todas las áreas de la automatización y robótica industrial.'\n    },\n    {\n        id: '2',\n        name: 'Jaime Villafruela',\n        position: 'Director Técnico',\n        image: 'https://picsum.photos/400/400?random=7',\n        bio: 'Especialista en sistemas PLC/SCADA con amplia experiencia en proyectos de gran envergadura.'\n    }\n];\nconst statistics = [\n    {\n        id: '1',\n        value: 50,\n        label: 'Proyectos Completados',\n        suffix: '+'\n    },\n    {\n        id: '2',\n        value: 98,\n        label: 'Satisfacción del Cliente',\n        suffix: '%'\n    },\n    {\n        id: '3',\n        value: 10,\n        label: 'Años de Experiencia',\n        suffix: '+'\n    },\n    {\n        id: '4',\n        value: 24,\n        label: 'Soporte Técnico',\n        suffix: '/7'\n    }\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/data.ts\n"));

/***/ })

});