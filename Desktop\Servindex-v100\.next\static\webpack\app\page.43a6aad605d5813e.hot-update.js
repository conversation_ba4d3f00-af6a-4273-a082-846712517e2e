"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/data.ts":
/*!*************************!*\
  !*** ./src/lib/data.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   blogPosts: () => (/* binding */ blogPosts),\n/* harmony export */   projects: () => (/* binding */ projects),\n/* harmony export */   services: () => (/* binding */ services),\n/* harmony export */   statistics: () => (/* binding */ statistics),\n/* harmony export */   teamMembers: () => (/* binding */ teamMembers)\n/* harmony export */ });\nconst services = [\n    {\n        id: '1',\n        title: 'Automatización Industrial',\n        description: 'Sistemas de automatización completos para optimizar procesos industriales y aumentar la eficiencia operativa.',\n        icon: 'Settings',\n        features: [\n            'Diseño de sistemas automatizados',\n            'Integración de sensores y actuadores',\n            'Optimización de procesos',\n            'Reducción de costos operativos'\n        ]\n    },\n    {\n        id: '2',\n        title: 'Robótica Industrial',\n        description: 'Implementación de soluciones robóticas avanzadas para manufactura y manipulación de materiales.',\n        icon: 'Bot',\n        features: [\n            'Robots colaborativos (cobots)',\n            'Sistemas de visión artificial',\n            'Programación de trayectorias',\n            'Mantenimiento predictivo'\n        ]\n    },\n    {\n        id: '3',\n        title: 'Programación PLC/SCADA',\n        description: 'Desarrollo y programación de sistemas de control distribuido para monitoreo y control de procesos industriales.',\n        icon: 'Code',\n        features: [\n            'Programación de PLCs',\n            'Desarrollo de interfaces SCADA',\n            'Sistemas de alarmas',\n            'Históricos y reportes'\n        ]\n    },\n    {\n        id: '4',\n        title: 'Cuadros Eléctricos',\n        description: 'Diseño, fabricación e instalación de cuadros eléctricos industriales con los más altos estándares de calidad.',\n        icon: 'Zap',\n        features: [\n            'Diseño según normativas',\n            'Fabricación personalizada',\n            'Instalación y puesta en marcha',\n            'Certificaciones de calidad'\n        ]\n    },\n    {\n        id: '5',\n        title: 'Consultoría Técnica',\n        description: 'Asesoramiento especializado en ingeniería industrial, optimización de procesos y transformación digital.',\n        icon: 'Users',\n        features: [\n            'Auditorías técnicas',\n            'Planes de modernización',\n            'Análisis de eficiencia',\n            'Formación especializada'\n        ]\n    },\n    {\n        id: '6',\n        title: 'Mantenimiento Industrial',\n        description: 'Servicios integrales de mantenimiento preventivo, predictivo y correctivo para equipos industriales.',\n        icon: 'Wrench',\n        features: [\n            'Mantenimiento preventivo',\n            'Diagnóstico predictivo',\n            'Reparaciones especializadas',\n            'Gestión de repuestos'\n        ]\n    }\n];\nconst projects = [\n    {\n        id: '1',\n        title: 'Automatización de Línea de Tomate Concentrado',\n        description: 'Optimización completa de la línea de producción de tomate concentrado mediante control automatizado y monitoreo en tiempo real.',\n        image: '/imagenes/1.jpg',\n        category: 'Alimentaria',\n        technologies: [\n            'Siemens Step7',\n            'Profibus',\n            'Intouch',\n            'SCADA',\n            'PLC'\n        ],\n        year: 2025,\n        client: 'Conesa Group'\n    },\n    {\n        id: '3',\n        title: 'Producción Automatizada de Tomate Frito',\n        description: 'Automatización integral del proceso de tomate frito, desde la cocción hasta el envasado final, garantizando calidad constante.',\n        image: '/imagenes/7.jpg',\n        category: 'Alimentaria',\n        technologies: [\n            'CX-Programmer',\n            'CX-Supervisor',\n            'Weidmuller',\n            'Instrumentación'\n        ],\n        year: 2025,\n        client: 'Tomcoex'\n    },\n    {\n        id: '7',\n        title: 'Sistema de Filtrado de Arena Industrial',\n        description: 'Automatización de filtrado industrial con control de presión y ciclos de retrolavado automáticos para mayor eficiencia.',\n        image: '/imagenes/6.jpg',\n        category: 'Fluidos',\n        technologies: [\n            'PLC Omron',\n            'CX-Supervisor',\n            'HMI Proface',\n            'Válvulas Automáticas'\n        ],\n        year: 2025,\n        client: 'Agraz'\n    },\n    {\n        id: '8',\n        title: 'Línea Automatizada de Pelado de Almendras',\n        description: 'Control automático de rodillos y detección de obstrucciones para un pelado seguro y eficiente.',\n        image: '/imagenes/5.jpg',\n        category: 'Alimentaria',\n        technologies: [\n            'PLC Omron',\n            'HMI Proface',\n            'Variadores de Frecuencia'\n        ],\n        year: 2025,\n        client: 'Acopaex'\n    },\n    {\n        id: '9',\n        title: 'Producción Automatizada de Sustratos',\n        description: 'Línea de mezclado y envasado de sustratos con control de pesaje, humedad y flujo de material optimizado.',\n        image: '/imagenes/3.jpg',\n        category: 'Industrial',\n        technologies: [\n            'Siemens S7-1200',\n            'Profinet',\n            'Arrancador Schneider',\n            'Variadores'\n        ],\n        year: 2025,\n        client: 'Sustratos de Extremadura'\n    },\n    {\n        id: '10',\n        title: 'Pasadoras y Refinadoras de Tomate Automatizadas',\n        description: 'Control preciso de caudal y proceso en pasadoras y refinadoras para garantizar la calidad del producto final.',\n        image: '/imagenes/8.jpg',\n        category: 'Alimentaria',\n        technologies: [\n            'PLC Omron',\n            'HMI Proface',\n            'Variadores de Frecuencia'\n        ],\n        year: 2025,\n        client: 'Conesa Group'\n    },\n    {\n        id: '11',\n        title: 'Sistema de Extracción de gases',\n        description: 'Automatización de ventilación industrial con control de caudal, presión y alarmas de seguridad en tiempo real.',\n        image: '/imagenes/4.jpg',\n        category: 'Fluidos',\n        technologies: [\n            'PLC Simenes 1500',\n            'Redundancia',\n            'HMI Unified',\n            'Valvulas Automáticas'\n        ],\n        year: 2025,\n        client: 'Diamond Foundry'\n    },\n    {\n        id: '12',\n        title: 'Control de Nivel y Bombeo con Grupo de Presión',\n        description: 'Sistema de control automático de nivel y bombeo presurizado con alarmas para evitar funcionamiento en vacío.',\n        image: 'https://picsum.photos/800/600?random=12',\n        category: 'Fluidos',\n        technologies: [\n            'PLC Siemens',\n            'HMI',\n            'Sensores de Nivel',\n            'Variadores de Frecuencia'\n        ],\n        year: 2025,\n        client: 'Diamond Foundry'\n    },\n    {\n        id: '2',\n        title: 'Sistema Modular de Etiquetado de Botellas',\n        description: 'Integración de un sistema modular de etiquetado con control sincronizado para líneas de envasado de alta eficiencia.',\n        image: 'https://picsum.photos/800/600?random=2',\n        category: 'Industrial',\n        technologies: [\n            'Siemens PLC',\n            'Servo Motor',\n            'Profinet',\n            'HMI',\n            'Sensores Ópticos'\n        ],\n        year: 2025,\n        client: 'Cave San José'\n    },\n    {\n        id: '4',\n        title: 'Línea Robotizada para Producción de Piezas',\n        description: 'Implementación de celdas robotizadas con sistemas de visión para fabricación de piezas con máxima precisión.',\n        image: 'https://picsum.photos/800/600?random=4',\n        category: 'Industrial',\n        technologies: [\n            'KUKA KRC4',\n            'ABB RobotStudio',\n            'PLC Siemens',\n            'Profinet'\n        ],\n        year: 2025,\n        client: 'Santiago Apostol'\n    },\n    {\n        id: '5',\n        title: 'Riego Inteligente con Control Remoto',\n        description: 'Sistema de riego automatizado con monitoreo remoto y programación flexible basado en condiciones del terreno.',\n        image: 'https://picsum.photos/800/600?random=5',\n        category: 'Fluidos',\n        technologies: [\n            'Arduino',\n            'Raspberry Pi',\n            'Flask',\n            'MQTT',\n            'Sensores de Humedad'\n        ],\n        year: 2025,\n        client: 'Elecex'\n    }\n];\nconst blogPosts = [\n    {\n        id: '1',\n        title: 'El Futuro de la Automatización Industrial',\n        excerpt: 'Exploramos las tendencias emergentes en automatización y cómo están transformando la industria.',\n        content: '',\n        image: 'https://picsum.photos/800/400?random=4',\n        author: 'Equipo Servindex',\n        publishedAt: '2024-01-15',\n        category: 'Automatización',\n        tags: [\n            'Industria 4.0',\n            'IoT',\n            'Automatización'\n        ],\n        readTime: 5\n    },\n    {\n        id: '2',\n        title: 'Mantenimiento Predictivo con IA',\n        excerpt: 'Cómo la inteligencia artificial está revolucionando el mantenimiento industrial.',\n        content: '',\n        image: 'https://picsum.photos/800/400?random=5',\n        author: 'Equipo Servindex',\n        publishedAt: '2024-01-10',\n        category: 'Mantenimiento',\n        tags: [\n            'IA',\n            'Mantenimiento',\n            'Predictivo'\n        ],\n        readTime: 7\n    }\n];\nconst teamMembers = [\n    {\n        id: '1',\n        name: 'Pablo Rebollo',\n        position: 'Director Técnico',\n        image: 'https://picsum.photos/400/400?random=6',\n        bio: 'Técnico Industrial con más de 6 años de experiencia en automatización industrial.'\n    },\n    {\n        id: '2',\n        name: 'Jaime Villafruela',\n        position: 'Director Técnico',\n        image: 'https://picsum.photos/400/400?random=7',\n        bio: 'Especialista en sistemas PLC/SCADA con amplia experiencia en proyectos de gran envergadura.'\n    }\n];\nconst statistics = [\n    {\n        id: '1',\n        value: 50,\n        label: 'Proyectos Completados',\n        suffix: '+'\n    },\n    {\n        id: '2',\n        value: 98,\n        label: 'Satisfacción del Cliente',\n        suffix: '%'\n    },\n    {\n        id: '3',\n        value: 10,\n        label: 'Años de Experiencia',\n        suffix: '+'\n    },\n    {\n        id: '4',\n        value: 24,\n        label: 'Soporte Técnico',\n        suffix: '/7'\n    }\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/data.ts\n"));

/***/ })

});