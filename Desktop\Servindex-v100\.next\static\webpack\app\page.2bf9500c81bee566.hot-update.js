"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/data.ts":
/*!*************************!*\
  !*** ./src/lib/data.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   blogPosts: () => (/* binding */ blogPosts),\n/* harmony export */   projects: () => (/* binding */ projects),\n/* harmony export */   services: () => (/* binding */ services),\n/* harmony export */   statistics: () => (/* binding */ statistics),\n/* harmony export */   teamMembers: () => (/* binding */ teamMembers)\n/* harmony export */ });\nconst services = [\n    {\n        id: '1',\n        title: 'Automatización Industrial',\n        description: 'Sistemas de automatización completos para optimizar procesos industriales y aumentar la eficiencia operativa.',\n        icon: 'Settings',\n        features: [\n            'Diseño de sistemas automatizados',\n            'Integración de sensores y actuadores',\n            'Optimización de procesos',\n            'Reducción de costos operativos'\n        ]\n    },\n    {\n        id: '2',\n        title: 'Robótica Industrial',\n        description: 'Implementación de soluciones robóticas avanzadas para manufactura y manipulación de materiales.',\n        icon: 'Bot',\n        features: [\n            'Robots colaborativos (cobots)',\n            'Sistemas de visión artificial',\n            'Programación de trayectorias',\n            'Mantenimiento predictivo'\n        ]\n    },\n    {\n        id: '3',\n        title: 'Programación PLC/SCADA',\n        description: 'Desarrollo y programación de sistemas de control distribuido para monitoreo y control de procesos industriales.',\n        icon: 'Code',\n        features: [\n            'Programación de PLCs',\n            'Desarrollo de interfaces SCADA',\n            'Sistemas de alarmas',\n            'Históricos y reportes'\n        ]\n    },\n    {\n        id: '4',\n        title: 'Cuadros Eléctricos',\n        description: 'Diseño, fabricación e instalación de cuadros eléctricos industriales con los más altos estándares de calidad.',\n        icon: 'Zap',\n        features: [\n            'Diseño según normativas',\n            'Fabricación personalizada',\n            'Instalación y puesta en marcha',\n            'Certificaciones de calidad'\n        ]\n    },\n    {\n        id: '5',\n        title: 'Consultoría Técnica',\n        description: 'Asesoramiento especializado en ingeniería industrial, optimización de procesos y transformación digital.',\n        icon: 'Users',\n        features: [\n            'Auditorías técnicas',\n            'Planes de modernización',\n            'Análisis de eficiencia',\n            'Formación especializada'\n        ]\n    },\n    {\n        id: '6',\n        title: 'Mantenimiento Industrial',\n        description: 'Servicios integrales de mantenimiento preventivo, predictivo y correctivo para equipos industriales.',\n        icon: 'Wrench',\n        features: [\n            'Mantenimiento preventivo',\n            'Diagnóstico predictivo',\n            'Reparaciones especializadas',\n            'Gestión de repuestos'\n        ]\n    }\n];\nconst projects = [\n    {\n        id: '1',\n        title: 'Automatización de Línea de Tomate Concentrado',\n        description: 'Optimización completa de la línea de producción de tomate concentrado mediante control automatizado y monitoreo en tiempo real.',\n        image: '/imagenes/1.jpg',\n        category: 'Alimentaria',\n        technologies: [\n            'Siemens Step7',\n            'Profibus',\n            'Intouch',\n            'SCADA',\n            'PLC'\n        ],\n        year: 2025,\n        client: 'Conesa Group'\n    },\n    {\n        id: '2',\n        title: 'Producción Automatizada de Tomate Frito',\n        description: 'Automatización integral del proceso de tomate frito, desde la cocción hasta el envasado final, garantizando calidad constante.',\n        image: '/imagenes/7.jpg',\n        category: 'Alimentaria',\n        technologies: [\n            'CX-Programmer',\n            'CX-Supervisor',\n            'Weidmuller',\n            'Instrumentación'\n        ],\n        year: 2024,\n        client: 'Tomcoex'\n    },\n    {\n        id: '3',\n        title: 'Sistema de Filtrado de Arena Industrial',\n        description: 'Automatización de filtrado industrial con control de presión y ciclos de retrolavado automáticos para mayor eficiencia.',\n        image: '/imagenes/6.jpg',\n        category: 'Fluidos',\n        technologies: [\n            'PLC Omron',\n            'CX-Supervisor',\n            'HMI Proface',\n            'Válvulas Automáticas'\n        ],\n        year: 2025,\n        client: 'Agraz'\n    },\n    {\n        id: '4',\n        title: 'Línea Automatizada de Pelado de Almendras',\n        description: 'Control automático de rodillos y detección de obstrucciones para un pelado seguro y eficiente.',\n        image: '/imagenes/5.jpg',\n        category: 'Alimentaria',\n        technologies: [\n            'PLC Omron',\n            'HMI Proface',\n            'Variadores de Frecuencia'\n        ],\n        year: 2024,\n        client: 'Acopaex'\n    },\n    {\n        id: '5',\n        title: 'Producción Automatizada de Sustratos',\n        description: 'Línea de mezclado y envasado de sustratos con control de pesaje, humedad y flujo de material optimizado.',\n        image: '/imagenes/3.jpg',\n        category: 'Industrial',\n        technologies: [\n            'Siemens S7-1200',\n            'Profinet',\n            'Arrancador Schneider',\n            'Variadores'\n        ],\n        year: 2023,\n        client: 'Sustratos de Extremadura'\n    },\n    {\n        id: '6',\n        title: 'Pasadoras y Refinadoras de Tomate Automatizadas',\n        description: 'Control preciso de caudal y proceso en pasadoras y refinadoras para garantizar la calidad del producto final.',\n        image: '/imagenes/8.jpg',\n        category: 'Alimentaria',\n        technologies: [\n            'PLC Omron',\n            'HMI Proface',\n            'Variadores de Frecuencia'\n        ],\n        year: 2024,\n        client: 'Conesa Group'\n    },\n    {\n        id: '7',\n        title: 'Sistema de Extracción de gases',\n        description: 'Automatización de ventilación industrial con control de caudal, presión y alarmas de seguridad en tiempo real.',\n        image: '/imagenes/4.jpg',\n        category: 'Fluidos',\n        technologies: [\n            'PLC Simenes 1500',\n            'Redundancia',\n            'HMI Unified',\n            'Valvulas Automáticas'\n        ],\n        year: 2024,\n        client: 'Diamond Foundry'\n    }\n];\nconst blogPosts = [\n    {\n        id: '1',\n        title: 'El Futuro de la Automatización Industrial',\n        excerpt: 'Exploramos las tendencias emergentes en automatización y cómo están transformando la industria.',\n        content: '',\n        image: '/post/1.jpg',\n        author: 'Equipo Servindex',\n        publishedAt: '2024-01-15',\n        category: 'Automatización',\n        tags: [\n            'Industria 4.0',\n            'IoT',\n            'Automatización'\n        ],\n        readTime: 5\n    },\n    {\n        id: '2',\n        title: 'Mantenimiento Predictivo con IA',\n        excerpt: 'Cómo la inteligencia artificial está revolucionando el mantenimiento industrial.',\n        content: '',\n        image: '/post/9.jpg',\n        author: 'Equipo Servindex',\n        publishedAt: '2024-01-10',\n        category: 'Mantenimiento',\n        tags: [\n            'IA',\n            'Mantenimiento',\n            'Predictivo'\n        ],\n        readTime: 7\n    },\n    {\n        id: '3',\n        title: 'Robots Colaborativos en la Industria',\n        excerpt: 'Los cobots están cambiando la forma de trabajar en fábricas y talleres.',\n        content: '',\n        image: 'https://picsum.photos/800/400?random=6',\n        author: 'Equipo Servindex',\n        publishedAt: '2024-02-01',\n        category: 'Robótica',\n        tags: [\n            'Cobots',\n            'Automatización',\n            'Industria 4.0'\n        ],\n        readTime: 6\n    },\n    {\n        id: '4',\n        title: 'Sensores Inteligentes y Control en Tiempo Real',\n        excerpt: 'La importancia de la sensórica avanzada para mejorar la eficiencia de las líneas de producción.',\n        content: '',\n        image: 'https://picsum.photos/800/400?random=7',\n        author: 'Equipo Servindex',\n        publishedAt: '2024-02-05',\n        category: 'Automatización',\n        tags: [\n            'Sensores',\n            'Control Industrial',\n            'Eficiencia'\n        ],\n        readTime: 5\n    },\n    {\n        id: '5',\n        title: 'Transformación Digital en Fábricas',\n        excerpt: 'Cómo digitalizar procesos industriales y aumentar la competitividad.',\n        content: '',\n        image: 'https://picsum.photos/800/400?random=8',\n        author: 'Equipo Servindex',\n        publishedAt: '2024-02-10',\n        category: 'Transformación Digital',\n        tags: [\n            'Digitalización',\n            'IoT',\n            'Industria'\n        ],\n        readTime: 6\n    },\n    {\n        id: '6',\n        title: 'SCADA y Monitoreo Remoto',\n        excerpt: 'Supervisión de procesos industriales desde cualquier lugar con sistemas SCADA modernos.',\n        content: '',\n        image: 'https://picsum.photos/800/400?random=9',\n        author: 'Equipo Servindex',\n        publishedAt: '2024-02-15',\n        category: 'Control Industrial',\n        tags: [\n            'SCADA',\n            'Monitoreo',\n            'Automatización'\n        ],\n        readTime: 5\n    },\n    {\n        id: '7',\n        title: 'Ciberseguridad en Sistemas Industriales',\n        excerpt: 'Proteger instalaciones industriales frente a amenazas digitales es clave en la Industria 4.0.',\n        content: '',\n        image: 'https://picsum.photos/800/400?random=10',\n        author: 'Equipo Servindex',\n        publishedAt: '2024-02-20',\n        category: 'Seguridad Industrial',\n        tags: [\n            'Ciberseguridad',\n            'Industria 4.0',\n            'Protección'\n        ],\n        readTime: 8\n    },\n    {\n        id: '8',\n        title: 'Eficiencia Energética en la Industria',\n        excerpt: 'Estrategias y tecnologías para reducir el consumo energético de las fábricas.',\n        content: '',\n        image: 'https://picsum.photos/800/400?random=11',\n        author: 'Equipo Servindex',\n        publishedAt: '2024-03-01',\n        category: 'Energía',\n        tags: [\n            'Eficiencia',\n            'Ahorro Energético',\n            'Industria'\n        ],\n        readTime: 6\n    },\n    {\n        id: '9',\n        title: 'Automatización de Líneas de Envasado',\n        excerpt: 'Optimizando la producción con sistemas automáticos en líneas de envasado.',\n        content: '',\n        image: '/post/9.jpg',\n        author: 'Equipo Servindex',\n        publishedAt: '2024-03-05',\n        category: 'Automatización',\n        tags: [\n            'Envasado',\n            'Automatización',\n            'Producción'\n        ],\n        readTime: 5\n    },\n    {\n        id: '10',\n        title: 'Gemelos Digitales en la Industria',\n        excerpt: 'Simulación avanzada de procesos industriales para reducir riesgos y costos.',\n        content: '',\n        image: '/post/10.jpg',\n        author: 'Equipo Servindex',\n        publishedAt: '2024-03-10',\n        category: 'Innovación',\n        tags: [\n            'Gemelo Digital',\n            'Industria 4.0',\n            'Simulación'\n        ],\n        readTime: 7\n    }\n];\nconst teamMembers = [\n    {\n        id: '1',\n        name: 'Pablo Rebollo',\n        position: 'Director Técnico',\n        image: '/imagenes/pablo.png',\n        bio: 'Técnico polivalente en todas las áreas de la automatización y robótica industrial.'\n    },\n    {\n        id: '2',\n        name: 'Jaime Villafruela',\n        position: 'Director Técnico',\n        image: 'https://picsum.photos/400/400?random=7',\n        bio: 'Especialista en sistemas PLC/SCADA con amplia experiencia en proyectos de gran envergadura.'\n    }\n];\nconst statistics = [\n    {\n        id: '1',\n        value: 50,\n        label: 'Proyectos Completados',\n        suffix: '+'\n    },\n    {\n        id: '2',\n        value: 98,\n        label: 'Satisfacción del Cliente',\n        suffix: '%'\n    },\n    {\n        id: '3',\n        value: 10,\n        label: 'Años de Experiencia',\n        suffix: '+'\n    },\n    {\n        id: '4',\n        value: 24,\n        label: 'Soporte Técnico',\n        suffix: '/7'\n    }\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/data.ts\n"));

/***/ })

});