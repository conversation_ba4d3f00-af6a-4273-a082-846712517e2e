// Email configuration and utilities
export interface EmailData {
  name: string;
  email: string;
  company?: string;
  phone?: string;
  service: string;
  message: string;
}

export interface EmailConfig {
  to: string;
  subject: string;
  html: string;
  text: string;
}

export function createEmailContent(data: EmailData): EmailConfig {
  const { name, email, company, phone, service, message } = data;
  
  const subject = `Nueva consulta de ${name} - ${service}`;
  
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #2563eb; border-bottom: 2px solid #2563eb; padding-bottom: 10px;">
        Nueva consulta desde servindex.es
      </h2>
      
      <div style="background-color: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
        <h3 style="color: #374151; margin-top: 0;">Información del Cliente</h3>
        <p><strong>Nombre:</strong> ${name}</p>
        <p><strong>Email:</strong> <a href="mailto:${email}">${email}</a></p>
        ${company ? `<p><strong>Empresa:</strong> ${company}</p>` : ''}
        ${phone ? `<p><strong>Teléfono:</strong> <a href="tel:${phone}">${phone}</a></p>` : ''}
      </div>

      <div style="background-color: #eff6ff; padding: 20px; border-radius: 8px; margin: 20px 0;">
        <h3 style="color: #374151; margin-top: 0;">Servicio Solicitado</h3>
        <p style="font-weight: bold; color: #2563eb;">${service}</p>
      </div>

      <div style="background-color: #f0fdf4; padding: 20px; border-radius: 8px; margin: 20px 0;">
        <h3 style="color: #374151; margin-top: 0;">Mensaje</h3>
        <p style="white-space: pre-wrap; line-height: 1.6;">${message}</p>
      </div>

      <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 30px 0;">
      
      <p style="color: #6b7280; font-size: 14px; text-align: center;">
        Enviado desde servindex.es el ${new Date().toLocaleString('es-ES', {
          timeZone: 'Europe/Madrid',
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        })}
      </p>
    </div>
  `;

  const text = `
Nueva consulta desde servindex.es

Información del Cliente:
- Nombre: ${name}
- Email: ${email}
${company ? `- Empresa: ${company}` : ''}
${phone ? `- Teléfono: ${phone}` : ''}

Servicio Solicitado: ${service}

Mensaje:
${message}

Enviado el ${new Date().toLocaleString('es-ES', { timeZone: 'Europe/Madrid' })}
  `;

  return {
    to: '<EMAIL>',
    subject,
    html,
    text
  };
}

// Validation functions
export function validateEmailData(data: EmailData): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!data.name || data.name.trim().length < 2) {
    errors.push('El nombre debe tener al menos 2 caracteres');
  }

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!data.email || !emailRegex.test(data.email)) {
    errors.push('Email inválido');
  }

  if (!data.service) {
    errors.push('Debe seleccionar un servicio');
  }

  if (!data.message || data.message.trim().length < 10) {
    errors.push('El mensaje debe tener al menos 10 caracteres');
  }

  if (data.phone) {
    const phoneRegex = /^(\+34|0034|34)?[6789]\d{8}$/;
    if (!phoneRegex.test(data.phone.replace(/\s/g, ''))) {
      errors.push('Teléfono español inválido');
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

// Gmail SMTP integration with Nodemailer
import nodemailer from 'nodemailer';

export async function sendWithGmailSMTP(config: EmailConfig): Promise<{ success: boolean; messageId?: string; error?: string }> {
  try {
    // Validate environment variables
    if (!process.env.GMAIL_USER || !process.env.GMAIL_APP_PASSWORD) {
      throw new Error('Faltan credenciales de Gmail en las variables de entorno');
    }

    // Clean app password (remove spaces)
    const cleanPassword = process.env.GMAIL_APP_PASSWORD.replace(/\s/g, '');

    console.log('Configurando Gmail SMTP...');
    console.log('Usuario:', process.env.GMAIL_USER);
    console.log('Contraseña configurada:', cleanPassword ? 'Sí' : 'No');

    // Create transporter with Gmail SMTP
    const transporter = nodemailer.createTransport({
      service: 'gmail',
      auth: {
        user: process.env.GMAIL_USER,
        pass: cleanPassword,
      },
    });

    console.log('Verificando conexión SMTP...');
    // Verify connection
    await transporter.verify();
    console.log('Conexión SMTP verificada correctamente');

    // Send email
    console.log('Enviando email...');
    const info = await transporter.sendMail({
      from: `"Servindex Web" <${process.env.GMAIL_USER}>`, // Use Gmail user as sender
      to: config.to,
      subject: config.subject,
      text: config.text,
      html: config.html,
    });

    console.log('Email enviado correctamente:', info.messageId);
    return {
      success: true,
      messageId: info.messageId,
    };

  } catch (error) {
    console.error('Error sending email with Gmail SMTP:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Error desconocido',
    };
  }
}

// Future: Add other email service integrations here
// export async function sendWithSendGrid(config: EmailConfig) { ... }
// export async function sendWithResend(config: EmailConfig) { ... }
