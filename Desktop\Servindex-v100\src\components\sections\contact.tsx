'use client';

import * as React from 'react';
import { motion } from 'framer-motion';
import { 
  Mail, 
  Phone, 
  MapPin, 
  Clock, 
  Send,
  CheckCircle,
  ArrowRight,
  MessageSquare,
  User,
  Building
} from 'lucide-react';
import { Button } from '@/components/ui/button';

const contactInfo = [
  {
    icon: Phone,
    title: 'Teléfono',
    details: '+34 661 143 257',
    description: 'Lun - Vie: 8:00 - 18:00',
  },
  {
    icon: Mail,
    title: 'Email',
    details: '<EMAIL>',
    description: 'Respuesta en 24h',
  },
  {
    icon: MapPin,
    title: 'Oficina',
    details: 'Badajoz, Extremadura',
    description: 'España',
  },
  {
    icon: Clock,
    title: '<PERSON><PERSON><PERSON>',
    details: '8:00 - 18:00',
    description: 'Lunes a Viernes',
  },
];

const services = [
  'Automatización Industrial',
  'Robótica Industrial',
  'Programación PLC/SCADA',
  'Cuadros Eléctricos',
  'Consultoría Técnica',
  'Mantenimiento Industrial',
  '<PERSON><PERSON> (especificar)',
];

export function ContactSection() {
  const [currentStep, setCurrentStep] = React.useState(1);
  const [formData, setFormData] = React.useState({
    name: '',
    email: '',
    company: '',
    phone: '',
    service: '',
    message: '',
  });
  const [isSubmitted, setIsSubmitted] = React.useState(false);
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [errors, setErrors] = React.useState<{[key: string]: string}>({});

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: '',
      });
    }
  };

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePhone = (phone: string): boolean => {
    if (!phone) return true; // Phone is optional
    const phoneRegex = /^(\+34|0034|34)?[6789]\d{8}$/;
    return phoneRegex.test(phone.replace(/\s/g, ''));
  };

  const validateStep = (step: number): boolean => {
    const newErrors: {[key: string]: string} = {};

    switch (step) {
      case 1:
        if (!formData.name.trim()) {
          newErrors.name = 'El nombre es obligatorio';
        } else if (formData.name.trim().length < 2) {
          newErrors.name = 'El nombre debe tener al menos 2 caracteres';
        }

        if (!formData.email.trim()) {
          newErrors.email = 'El email es obligatorio';
        } else if (!validateEmail(formData.email)) {
          newErrors.email = 'Por favor, introduce un email válido';
        }

        if (formData.phone && !validatePhone(formData.phone)) {
          newErrors.phone = 'Por favor, introduce un teléfono español válido';
        }
        break;

      case 2:
        if (!formData.service) {
          newErrors.service = 'Por favor, selecciona un servicio';
        }
        break;

      case 3:
        if (!formData.message.trim()) {
          newErrors.message = 'El mensaje es obligatorio';
        } else if (formData.message.trim().length < 10) {
          newErrors.message = 'El mensaje debe tener al menos 10 caracteres';
        }
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate all steps before submitting
    const isValid = validateStep(1) && validateStep(2) && validateStep(3);
    if (!isValid) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Prepare email content
      const emailContent = {
        to: '<EMAIL>',
        subject: `Nueva consulta de ${formData.name} - ${formData.service}`,
        html: `
          <h2>Nueva consulta desde la web</h2>
          <p><strong>Nombre:</strong> ${formData.name}</p>
          <p><strong>Email:</strong> ${formData.email}</p>
          ${formData.company ? `<p><strong>Empresa:</strong> ${formData.company}</p>` : ''}
          ${formData.phone ? `<p><strong>Teléfono:</strong> ${formData.phone}</p>` : ''}
          <p><strong>Servicio solicitado:</strong> ${formData.service}</p>
          <p><strong>Mensaje:</strong></p>
          <p>${formData.message.replace(/\n/g, '<br>')}</p>
          <hr>
          <p><small>Enviado desde servindex.es el ${new Date().toLocaleString('es-ES')}</small></p>
        `
      };

      // Send email using mailto (fallback) or a service
      // For now, we'll use a simple mailto approach
      const mailtoLink = `mailto:<EMAIL>?subject=${encodeURIComponent(emailContent.subject)}&body=${encodeURIComponent(
        `Nueva consulta desde la web\n\n` +
        `Nombre: ${formData.name}\n` +
        `Email: ${formData.email}\n` +
        `${formData.company ? `Empresa: ${formData.company}\n` : ''}` +
        `${formData.phone ? `Teléfono: ${formData.phone}\n` : ''}` +
        `Servicio solicitado: ${formData.service}\n\n` +
        `Mensaje:\n${formData.message}\n\n` +
        `Enviado desde servindex.es el ${new Date().toLocaleString('es-ES')}`
      )}`;

      // Open mailto link
      window.location.href = mailtoLink;

      // Show success message
      setIsSubmitted(true);

      // Reset form after 5 seconds
      setTimeout(() => {
        setIsSubmitted(false);
        setCurrentStep(1);
        setFormData({
          name: '',
          email: '',
          company: '',
          phone: '',
          service: '',
          message: '',
        });
        setErrors({});
      }, 5000);

    } catch (error) {
      console.error('Error sending email:', error);
      alert('Hubo un error al enviar el mensaje. Por favor, inténtalo de nuevo o contacta <NAME_EMAIL>');
    } finally {
      setIsSubmitting(false);
    }
  };

  const nextStep = () => {
    if (validateStep(currentStep) && currentStep < 3) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) setCurrentStep(currentStep - 1);
  };

  const isStepValid = () => {
    switch (currentStep) {
      case 1:
        return formData.name.trim() && formData.email.trim() && validateEmail(formData.email) && (!formData.phone || validatePhone(formData.phone));
      case 2:
        return formData.service !== '';
      case 3:
        return formData.message.trim().length >= 10;
      default:
        return false;
    }
  };

  if (isSubmitted) {
    return (
      <section id="contacto" className="py-20 lg:py-32 bg-white dark:bg-gray-900 relative overflow-hidden">
        <div className="absolute inset-0 industrial-grid opacity-10" />
        <div className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
          >
            <div className="w-24 h-24 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-8">
              <CheckCircle className="w-12 h-12 text-white" />
            </div>
            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-6">
              ¡Mensaje Enviado!
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">
              Gracias por contactarnos. Nuestro equipo se pondrá en contacto contigo en las próximas 24 horas.
            </p>
            <div className="animate-pulse">
              <div className="w-16 h-1 bg-gradient-to-r from-blue-600 to-electric-500 rounded-full mx-auto"></div>
            </div>
          </motion.div>
        </div>
      </section>
    );
  }

  return (
    <section id="contacto" className="py-20 lg:py-32 bg-white dark:bg-gray-900 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 industrial-grid opacity-10" />
      <div className="absolute top-20 right-10 w-72 h-72 bg-electric-500/5 rounded-full blur-3xl" />
      <div className="absolute bottom-20 left-10 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl" />
      
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          className="text-center mb-16 lg:mb-20"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <motion.div
            className="inline-flex items-center px-4 py-2 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 text-sm font-medium mb-6"
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <MessageSquare className="w-4 h-4 mr-2" />
            Contacto
          </motion.div>
          
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 dark:text-white mb-6">
            Hablemos de tu <span className="gradient-text">Proyecto</span>
          </h2>
          
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed">
            ¿Listo para transformar tu industria? Nuestro equipo de expertos está aquí 
            para ayudarte a encontrar la solución perfecta.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
          {/* Contact Info */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-8">
              Información de Contacto
            </h3>
            
            <div className="space-y-6 mb-12">
              {contactInfo.map((info, index) => (
                <motion.div
                  key={index}
                  className="flex items-start space-x-4 group"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  whileHover={{ x: 10 }}
                >
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-600 to-electric-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <info.icon className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
                      {info.title}
                    </h4>
                    <p className="text-blue-600 dark:text-electric-400 font-medium">
                      {info.details}
                    </p>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      {info.description}
                    </p>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Map Placeholder */}
            <motion.div
              className="bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700 rounded-2xl h-80 overflow-hidden"
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              viewport={{ once: true }}
            >
              <iframe
                title="Mi ubicación"
                src="https://www.google.com/maps/embed?pb=!1m14!1m12!1m3!1d99395.277276956!2d-7.013700979616849!3d38.87589860228578!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!5e0!3m2!1ses!2ses!4v1754265423211!5m2!1ses!2ses"
                width="100%"
                height="100%"
                style={{ border: 0 }}
                allowFullScreen
                loading="lazy"
                referrerPolicy="no-referrer-when-downgrade"
                className="rounded-2xl"
              ></iframe>
            </motion.div>
          </motion.div>

          {/* Contact Form */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-2xl border border-gray-200 dark:border-gray-700">
              {/* Progress Bar */}
              <div className="mb-8">
                <div className="flex justify-between items-center mb-4">
                  <span className="text-sm font-medium text-gray-600 dark:text-gray-300">
                    Paso {currentStep} de 3
                  </span>
                  <span className="text-sm font-medium text-blue-600 dark:text-electric-400">
                    {Math.round((currentStep / 3) * 100)}%
                  </span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <motion.div
                    className="bg-gradient-to-r from-blue-600 to-electric-500 h-2 rounded-full"
                    initial={{ width: '33%' }}
                    animate={{ width: `${(currentStep / 3) * 100}%` }}
                    transition={{ duration: 0.3 }}
                  />
                </div>
              </div>

              <form onSubmit={handleSubmit}>
                {/* Step 1: Personal Info */}
                {currentStep === 1 && (
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    transition={{ duration: 0.3 }}
                  >
                    <h4 className="text-xl font-bold text-gray-900 dark:text-white mb-6">
                      Información Personal
                    </h4>
                    
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          Nombre completo *
                        </label>
                        <div className="relative">
                          <User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                          <input
                            type="text"
                            name="name"
                            value={formData.name}
                            onChange={handleInputChange}
                            className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${
                              errors.name
                                ? 'border-red-500 focus:ring-red-500'
                                : 'border-gray-300 dark:border-gray-600 focus:ring-blue-500'
                            }`}
                            placeholder="Tu nombre completo"
                            required
                          />
                        </div>
                        {errors.name && (
                          <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.name}</p>
                        )}
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          Email *
                        </label>
                        <div className="relative">
                          <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                          <input
                            type="email"
                            name="email"
                            value={formData.email}
                            onChange={handleInputChange}
                            className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${
                              errors.email
                                ? 'border-red-500 focus:ring-red-500'
                                : 'border-gray-300 dark:border-gray-600 focus:ring-blue-500'
                            }`}
                            placeholder="<EMAIL>"
                            required
                          />
                        </div>
                        {errors.email && (
                          <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.email}</p>
                        )}
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Empresa
                          </label>
                          <div className="relative">
                            <Building className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                            <input
                              type="text"
                              name="company"
                              value={formData.company}
                              onChange={handleInputChange}
                              className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                              placeholder="Tu empresa"
                            />
                          </div>
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Teléfono
                          </label>
                          <div className="relative">
                            <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                            <input
                              type="tel"
                              name="phone"
                              value={formData.phone}
                              onChange={handleInputChange}
                              className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${
                                errors.phone
                                  ? 'border-red-500 focus:ring-red-500'
                                  : 'border-gray-300 dark:border-gray-600 focus:ring-blue-500'
                              }`}
                              placeholder="+34 XXX XXX XXX"
                              requie
                            />
                          </div>
                          {errors.phone && (
                            <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.phone}</p>
                          )}
                        </div>
                      </div>
                    </div>
                  </motion.div>
                )}

                {/* Step 2: Service Selection */}
                {currentStep === 2 && (
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    transition={{ duration: 0.3 }}
                  >
                    <h4 className="text-xl font-bold text-gray-900 dark:text-white mb-6">
                      ¿En qué podemos ayudarte?
                    </h4>
                    
                    <div className="space-y-3">
                      {services.map((service, index) => (
                        <motion.label
                          key={index}
                          className={`flex items-center p-4 border rounded-lg cursor-pointer transition-all duration-200 ${
                            formData.service === service
                              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                              : 'border-gray-300 dark:border-gray-600 hover:border-blue-300 dark:hover:border-blue-500'
                          }`}
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                        >
                          <input
                            type="radio"
                            name="service"
                            value={service}
                            checked={formData.service === service}
                            onChange={handleInputChange}
                            className="sr-only"
                          />
                          <div className={`w-4 h-4 rounded-full border-2 mr-3 ${
                            formData.service === service
                              ? 'border-blue-500 bg-blue-500'
                              : 'border-gray-300 dark:border-gray-600'
                          }`}>
                            {formData.service === service && (
                              <div className="w-2 h-2 bg-white rounded-full mx-auto mt-0.5" />
                            )}
                          </div>
                          <span className="text-gray-900 dark:text-white font-medium">
                            {service}
                          </span>
                        </motion.label>
                      ))}
                    </div>
                    {errors.service && (
                      <p className="mt-2 text-sm text-red-600 dark:text-red-400">{errors.service}</p>
                    )}
                  </motion.div>
                )}

                {/* Step 3: Message */}
                {currentStep === 3 && (
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    transition={{ duration: 0.3 }}
                  >
                    <h4 className="text-xl font-bold text-gray-900 dark:text-white mb-6">
                      Cuéntanos sobre tu proyecto
                    </h4>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Mensaje *
                      </label>
                      <textarea
                        name="message"
                        value={formData.message}
                        onChange={handleInputChange}
                        rows={6}
                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white resize-none ${
                          errors.message
                            ? 'border-red-500 focus:ring-red-500'
                            : 'border-gray-300 dark:border-gray-600 focus:ring-blue-500'
                        }`}
                        placeholder="Describe tu proyecto, necesidades específicas, plazos, presupuesto aproximado, etc."
                        required
                      />
                      {errors.message && (
                        <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.message}</p>
                      )}
                    </div>
                  </motion.div>
                )}

                {/* Navigation Buttons */}
                <div className="flex justify-between mt-8">
                  <Button
                    type="button"
                    onClick={prevStep}
                    variant="outline"
                    className={`${currentStep === 1 ? 'invisible' : ''}`}
                  >
                    Anterior
                  </Button>
                  
                  {currentStep < 3 ? (
                    <Button
                      type="button"
                      onClick={nextStep}
                      disabled={!isStepValid()}
                      className="bg-gradient-to-r from-blue-600 to-electric-500 hover:from-blue-700 hover:to-electric-600 text-white font-semibold px-6 py-2 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Siguiente
                      <ArrowRight className="ml-2 w-4 h-4" />
                    </Button>
                  ) : (
                    <Button
                      type="submit"
                      disabled={!isStepValid() || isSubmitting}
                      className="bg-gradient-to-r from-blue-600 to-electric-500 hover:from-blue-700 hover:to-electric-600 text-white font-semibold px-6 py-2 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isSubmitting ? 'Enviando...' : 'Enviar Mensaje'}
                      <Send className={`ml-2 w-4 h-4 ${isSubmitting ? 'animate-pulse' : ''}`} />
                    </Button>
                  )}
                </div>
              </form>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
