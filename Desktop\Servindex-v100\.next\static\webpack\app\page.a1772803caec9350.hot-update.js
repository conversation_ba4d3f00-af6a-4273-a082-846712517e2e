"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/sections/projects.tsx":
/*!**********************************************!*\
  !*** ./src/components/sections/projects.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProjectsSection: () => (/* binding */ ProjectsSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Cpu_ExternalLink_Link_Play_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Cpu,ExternalLink,Link,Play!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cpu.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Cpu_ExternalLink_Link_Play_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Cpu,ExternalLink,Link,Play!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Cpu_ExternalLink_Link_Play_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Cpu,ExternalLink,Link,Play!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Cpu_ExternalLink_Link_Play_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Cpu,ExternalLink,Link,Play!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Cpu_ExternalLink_Link_Play_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Cpu,ExternalLink,Link,Play!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Cpu_ExternalLink_Link_Play_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Cpu,ExternalLink,Link,Play!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Cpu_ExternalLink_Link_Play_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Cpu,ExternalLink,Link,Play!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Cpu_ExternalLink_Link_Play_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Cpu,ExternalLink,Link,Play!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/link.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Cpu_ExternalLink_Link_Play_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Cpu,ExternalLink,Link,Play!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _lib_data__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/data */ \"(app-pages-browser)/./src/lib/data.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ ProjectsSection auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst categories = [\n    'Todos',\n    'Alimentaria',\n    'Fluidos',\n    'Industrial'\n];\nfunction ProjectsSection() {\n    _s();\n    const [selectedCategory, setSelectedCategory] = react__WEBPACK_IMPORTED_MODULE_1__.useState('Todos');\n    const [currentProject, setCurrentProject] = react__WEBPACK_IMPORTED_MODULE_1__.useState(0);\n    const filteredProjects = selectedCategory === 'Todos' ? _lib_data__WEBPACK_IMPORTED_MODULE_2__.projects : _lib_data__WEBPACK_IMPORTED_MODULE_2__.projects.filter((project)=>project.category === selectedCategory);\n    const nextProject = ()=>{\n        setCurrentProject((prev)=>(prev + 1) % filteredProjects.length);\n    };\n    const prevProject = ()=>{\n        setCurrentProject((prev)=>(prev - 1 + filteredProjects.length) % filteredProjects.length);\n    };\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"ProjectsSection.useEffect\": ()=>{\n            setCurrentProject(0);\n        }\n    }[\"ProjectsSection.useEffect\"], [\n        selectedCategory\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"ProjectsSection.useEffect\": ()=>{\n            const timer = setInterval(nextProject, 5000);\n            return ({\n                \"ProjectsSection.useEffect\": ()=>clearInterval(timer)\n            })[\"ProjectsSection.useEffect\"];\n        }\n    }[\"ProjectsSection.useEffect\"], [\n        filteredProjects.length\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"proyectos\",\n        className: \"py-20 lg:py-32 bg-gray-50 dark:bg-gray-800 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 industrial-grid opacity-20\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        className: \"text-center mb-16 lg:mb-20\",\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                className: \"inline-flex items-center px-4 py-2 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 text-sm font-medium mb-6\",\n                                initial: {\n                                    opacity: 0,\n                                    scale: 0.8\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    scale: 1\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.2\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Cpu_ExternalLink_Link_Play_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Casos de \\xc9xito\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 dark:text-white mb-6\",\n                                children: [\n                                    \"Proyectos que \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"gradient-text\",\n                                        children: \"Transforman\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 27\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Industrias\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed\",\n                                children: \"Descubre c\\xf3mo hemos ayudado a empresas l\\xedderes a optimizar sus procesos y aumentar su productividad con soluciones innovadoras.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        className: \"flex flex-wrap justify-center gap-4 mb-12\",\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.6,\n                            delay: 0.3\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                onClick: ()=>setSelectedCategory(category),\n                                className: \"px-6 py-3 rounded-full font-medium transition-all duration-300 \".concat(selectedCategory === category ? 'bg-gradient-to-r from-blue-600 to-electric-500 text-white shadow-lg' : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 border border-gray-200 dark:border-gray-600'),\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                whileTap: {\n                                    scale: 0.95\n                                },\n                                children: category\n                            }, category, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n                                mode: \"wait\",\n                                children: filteredProjects.length > 0 && filteredProjects[currentProject] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: 300\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    exit: {\n                                        opacity: 0,\n                                        x: -300\n                                    },\n                                    transition: {\n                                        duration: 0.5\n                                    },\n                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                            className: \"relative group\",\n                                            whileHover: {\n                                                scale: 1.02\n                                            },\n                                            transition: {\n                                                duration: 0.3\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative aspect-[4/3] rounded-2xl overflow-hidden shadow-2xl\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            src: filteredProjects[currentProject].image,\n                                                            alt: filteredProjects[currentProject].title,\n                                                            fill: true,\n                                                            sizes: \"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\",\n                                                            className: \"object-cover transition-transform duration-500 group-hover:scale-110\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                            lineNumber: 128,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                            lineNumber: 135,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                                            className: \"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                                            whileHover: {\n                                                                scale: 1.1\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Cpu_ExternalLink_Link_Play_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"w-6 h-6 text-white ml-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                                    lineNumber: 143,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                                lineNumber: 142,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                            lineNumber: 138,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-4 left-4 px-3 py-1 bg-gradient-to-r from-blue-600 to-electric-500 text-white text-sm font-medium rounded-full\",\n                                                    children: filteredProjects[currentProject].category\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                                    initial: {\n                                                        opacity: 0,\n                                                        y: 20\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        y: 0\n                                                    },\n                                                    transition: {\n                                                        delay: 0.2\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4\",\n                                                            children: filteredProjects[currentProject].title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg text-gray-600 dark:text-gray-300 leading-relaxed mb-6\",\n                                                            children: filteredProjects[currentProject].description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                            lineNumber: 164,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                                    className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                                                    initial: {\n                                                        opacity: 0,\n                                                        y: 20\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        y: 0\n                                                    },\n                                                    transition: {\n                                                        delay: 0.3\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Cpu_ExternalLink_Link_Play_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"w-5 h-5 text-blue-600 dark:text-electric-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                                    lineNumber: 177,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600 dark:text-gray-300\",\n                                                                    children: filteredProjects[currentProject].year\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                                    lineNumber: 178,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                            lineNumber: 176,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        filteredProjects[currentProject].client && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Cpu_ExternalLink_Link_Play_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"w-5 h-5 text-blue-600 dark:text-electric-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                                    lineNumber: 184,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600 dark:text-gray-300\",\n                                                                    children: filteredProjects[currentProject].client\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                                    lineNumber: 185,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                            lineNumber: 183,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                                    initial: {\n                                                        opacity: 0,\n                                                        y: 20\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        y: 0\n                                                    },\n                                                    transition: {\n                                                        delay: 0.4\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-lg font-semibold text-gray-900 dark:text-white mb-3\",\n                                                            children: \"Tecnolog\\xedas Utilizadas:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap gap-2\",\n                                                            children: filteredProjects[currentProject].technologies.map((tech, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.span, {\n                                                                    className: \"px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 text-sm font-medium rounded-full\",\n                                                                    initial: {\n                                                                        opacity: 0,\n                                                                        scale: 0.8\n                                                                    },\n                                                                    animate: {\n                                                                        opacity: 1,\n                                                                        scale: 1\n                                                                    },\n                                                                    transition: {\n                                                                        delay: 0.5 + index * 0.1\n                                                                    },\n                                                                    whileHover: {\n                                                                        scale: 1.05\n                                                                    },\n                                                                    children: tech\n                                                                }, index, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                                    lineNumber: 203,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                            lineNumber: 201,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                                    initial: {\n                                                        opacity: 0,\n                                                        y: 20\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        y: 0\n                                                    },\n                                                    transition: {\n                                                        delay: 0.6\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        className: \"bg-gradient-to-r from-blue-600 to-electric-500 hover:from-blue-700 hover:to-electric-600 text-white font-semibold px-6 py-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-300\",\n                                                        children: [\n                                                            \"Ver Caso Completo\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Cpu_ExternalLink_Link_Play_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"ml-2 w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                                lineNumber: 227,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, \"\".concat(selectedCategory, \"-\").concat(currentProject), true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, this),\n                            filteredProjects.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                        onClick: prevProject,\n                                        className: \"absolute left-4 top-1/2 -translate-y-1/2 w-12 h-12 bg-white dark:bg-gray-800 rounded-full shadow-lg flex items-center justify-center hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200 z-10\",\n                                        whileHover: {\n                                            scale: 1.1\n                                        },\n                                        whileTap: {\n                                            scale: 0.9\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Cpu_ExternalLink_Link_Play_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-6 h-6 text-gray-600 dark:text-gray-300\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                        onClick: nextProject,\n                                        className: \"absolute right-4 top-1/2 -translate-y-1/2 w-12 h-12 bg-white dark:bg-gray-800 rounded-full shadow-lg flex items-center justify-center hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200 z-10\",\n                                        whileHover: {\n                                            scale: 1.1\n                                        },\n                                        whileTap: {\n                                            scale: 0.9\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Cpu_ExternalLink_Link_Play_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"w-6 h-6 text-gray-600 dark:text-gray-300\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, this),\n                    filteredProjects.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        className: \"flex justify-center space-x-2 mt-8\",\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        transition: {\n                            delay: 0.8\n                        },\n                        children: filteredProjects.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                onClick: ()=>setCurrentProject(index),\n                                className: \"w-3 h-3 rounded-full transition-all duration-300 \".concat(index === currentProject ? 'bg-gradient-to-r from-blue-600 to-electric-500 scale-125' : 'bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500'),\n                                whileHover: {\n                                    scale: 1.2\n                                },\n                                whileTap: {\n                                    scale: 0.9\n                                }\n                            }, index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                        lineNumber: 261,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        className: \"text-center mt-16 lg:mt-20\",\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8,\n                            delay: 0.4\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-gray-600 dark:text-gray-300 mb-8\",\n                                children: \"\\xbfListo para ser nuestro pr\\xf3ximo caso de \\xe9xito?\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                size: \"lg\",\n                                className: \"bg-gradient-to-r from-blue-600 to-electric-500 hover:from-blue-700 hover:to-electric-600 text-white font-semibold px-8 py-4 rounded-full shadow-xl hover:shadow-2xl transition-all duration-300\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Cpu_ExternalLink_Link_Play_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    href: \"#contacto\",\n                                    children: [\n                                        \"Iniciar Mi Proyecto\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Cpu_ExternalLink_Link_Play_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"ml-2 w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, this);\n}\n_s(ProjectsSection, \"OGC6bCZmR6xCKp4smftKUdaqSE4=\");\n_c = ProjectsSection;\nvar _c;\n$RefreshReg$(_c, \"ProjectsSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/projects.tsx\n"));

/***/ })

});