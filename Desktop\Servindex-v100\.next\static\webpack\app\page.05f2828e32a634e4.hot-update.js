"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/link.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/link.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Link)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.533.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71\",\n            key: \"1cjeqo\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71\",\n            key: \"19qd67\"\n        }\n    ]\n];\nconst Link = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"link\", __iconNode);\n //# sourceMappingURL=link.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/link.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/sections/projects.tsx":
/*!**********************************************!*\
  !*** ./src/components/sections/projects.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProjectsSection: () => (/* binding */ ProjectsSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Cpu_ExternalLink_Link_Play_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Cpu,ExternalLink,Link,Play!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cpu.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Cpu_ExternalLink_Link_Play_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Cpu,ExternalLink,Link,Play!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Cpu_ExternalLink_Link_Play_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Cpu,ExternalLink,Link,Play!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Cpu_ExternalLink_Link_Play_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Cpu,ExternalLink,Link,Play!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Cpu_ExternalLink_Link_Play_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Cpu,ExternalLink,Link,Play!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Cpu_ExternalLink_Link_Play_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Cpu,ExternalLink,Link,Play!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Cpu_ExternalLink_Link_Play_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Cpu,ExternalLink,Link,Play!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Cpu_ExternalLink_Link_Play_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Cpu,ExternalLink,Link,Play!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/link.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Cpu_ExternalLink_Link_Play_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Building,Calendar,ChevronLeft,ChevronRight,Cpu,ExternalLink,Link,Play!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _lib_data__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/data */ \"(app-pages-browser)/./src/lib/data.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ ProjectsSection auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst categories = [\n    'Todos',\n    'Alimentaria',\n    'Fluidos',\n    'Industrial'\n];\nfunction ProjectsSection() {\n    _s();\n    const [selectedCategory, setSelectedCategory] = react__WEBPACK_IMPORTED_MODULE_1__.useState('Todos');\n    const [currentProject, setCurrentProject] = react__WEBPACK_IMPORTED_MODULE_1__.useState(0);\n    const filteredProjects = selectedCategory === 'Todos' ? _lib_data__WEBPACK_IMPORTED_MODULE_2__.projects : _lib_data__WEBPACK_IMPORTED_MODULE_2__.projects.filter((project)=>project.category === selectedCategory);\n    const nextProject = ()=>{\n        setCurrentProject((prev)=>(prev + 1) % filteredProjects.length);\n    };\n    const prevProject = ()=>{\n        setCurrentProject((prev)=>(prev - 1 + filteredProjects.length) % filteredProjects.length);\n    };\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"ProjectsSection.useEffect\": ()=>{\n            setCurrentProject(0);\n        }\n    }[\"ProjectsSection.useEffect\"], [\n        selectedCategory\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"ProjectsSection.useEffect\": ()=>{\n            const timer = setInterval(nextProject, 5000);\n            return ({\n                \"ProjectsSection.useEffect\": ()=>clearInterval(timer)\n            })[\"ProjectsSection.useEffect\"];\n        }\n    }[\"ProjectsSection.useEffect\"], [\n        filteredProjects.length\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"proyectos\",\n        className: \"py-20 lg:py-32 bg-gray-50 dark:bg-gray-800 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 industrial-grid opacity-20\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        className: \"text-center mb-16 lg:mb-20\",\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                className: \"inline-flex items-center px-4 py-2 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 text-sm font-medium mb-6\",\n                                initial: {\n                                    opacity: 0,\n                                    scale: 0.8\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    scale: 1\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.2\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Cpu_ExternalLink_Link_Play_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Casos de \\xc9xito\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 dark:text-white mb-6\",\n                                children: [\n                                    \"Proyectos que \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"gradient-text\",\n                                        children: \"Transforman\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 27\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Industrias\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed\",\n                                children: \"Descubre c\\xf3mo hemos ayudado a empresas l\\xedderes a optimizar sus procesos y aumentar su productividad con soluciones innovadoras.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        className: \"flex flex-wrap justify-center gap-4 mb-12\",\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.6,\n                            delay: 0.3\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                onClick: ()=>setSelectedCategory(category),\n                                className: \"px-6 py-3 rounded-full font-medium transition-all duration-300 \".concat(selectedCategory === category ? 'bg-gradient-to-r from-blue-600 to-electric-500 text-white shadow-lg' : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 border border-gray-200 dark:border-gray-600'),\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                whileTap: {\n                                    scale: 0.95\n                                },\n                                children: category\n                            }, category, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n                                mode: \"wait\",\n                                children: filteredProjects.length > 0 && filteredProjects[currentProject] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: 300\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    exit: {\n                                        opacity: 0,\n                                        x: -300\n                                    },\n                                    transition: {\n                                        duration: 0.5\n                                    },\n                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                            className: \"relative group\",\n                                            whileHover: {\n                                                scale: 1.02\n                                            },\n                                            transition: {\n                                                duration: 0.3\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative aspect-[4/3] rounded-2xl overflow-hidden shadow-2xl\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            src: filteredProjects[currentProject].image,\n                                                            alt: filteredProjects[currentProject].title,\n                                                            fill: true,\n                                                            sizes: \"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\",\n                                                            className: \"object-cover transition-transform duration-500 group-hover:scale-110\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                            lineNumber: 128,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                            lineNumber: 135,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                                            className: \"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                                            whileHover: {\n                                                                scale: 1.1\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Cpu_ExternalLink_Link_Play_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"w-6 h-6 text-white ml-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                                    lineNumber: 143,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                                lineNumber: 142,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                            lineNumber: 138,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-4 left-4 px-3 py-1 bg-gradient-to-r from-blue-600 to-electric-500 text-white text-sm font-medium rounded-full\",\n                                                    children: filteredProjects[currentProject].category\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                                    initial: {\n                                                        opacity: 0,\n                                                        y: 20\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        y: 0\n                                                    },\n                                                    transition: {\n                                                        delay: 0.2\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4\",\n                                                            children: filteredProjects[currentProject].title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg text-gray-600 dark:text-gray-300 leading-relaxed mb-6\",\n                                                            children: filteredProjects[currentProject].description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                            lineNumber: 164,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                                    className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                                                    initial: {\n                                                        opacity: 0,\n                                                        y: 20\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        y: 0\n                                                    },\n                                                    transition: {\n                                                        delay: 0.3\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Cpu_ExternalLink_Link_Play_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"w-5 h-5 text-blue-600 dark:text-electric-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                                    lineNumber: 177,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600 dark:text-gray-300\",\n                                                                    children: filteredProjects[currentProject].year\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                                    lineNumber: 178,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                            lineNumber: 176,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        filteredProjects[currentProject].client && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Cpu_ExternalLink_Link_Play_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"w-5 h-5 text-blue-600 dark:text-electric-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                                    lineNumber: 184,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600 dark:text-gray-300\",\n                                                                    children: filteredProjects[currentProject].client\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                                    lineNumber: 185,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                            lineNumber: 183,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                                    initial: {\n                                                        opacity: 0,\n                                                        y: 20\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        y: 0\n                                                    },\n                                                    transition: {\n                                                        delay: 0.4\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-lg font-semibold text-gray-900 dark:text-white mb-3\",\n                                                            children: \"Tecnolog\\xedas Utilizadas:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap gap-2\",\n                                                            children: filteredProjects[currentProject].technologies.map((tech, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.span, {\n                                                                    className: \"px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 text-sm font-medium rounded-full\",\n                                                                    initial: {\n                                                                        opacity: 0,\n                                                                        scale: 0.8\n                                                                    },\n                                                                    animate: {\n                                                                        opacity: 1,\n                                                                        scale: 1\n                                                                    },\n                                                                    transition: {\n                                                                        delay: 0.5 + index * 0.1\n                                                                    },\n                                                                    whileHover: {\n                                                                        scale: 1.05\n                                                                    },\n                                                                    children: tech\n                                                                }, index, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                                    lineNumber: 203,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                            lineNumber: 201,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                                    initial: {\n                                                        opacity: 0,\n                                                        y: 20\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        y: 0\n                                                    },\n                                                    transition: {\n                                                        delay: 0.6\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        className: \"bg-gradient-to-r from-blue-600 to-electric-500 hover:from-blue-700 hover:to-electric-600 text-white font-semibold px-6 py-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-300\",\n                                                        children: [\n                                                            \"Ver Caso Completo\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Cpu_ExternalLink_Link_Play_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"ml-2 w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                                lineNumber: 227,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, \"\".concat(selectedCategory, \"-\").concat(currentProject), true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, this),\n                            filteredProjects.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                        onClick: prevProject,\n                                        className: \"absolute left-4 top-1/2 -translate-y-1/2 w-12 h-12 bg-white dark:bg-gray-800 rounded-full shadow-lg flex items-center justify-center hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200 z-10\",\n                                        whileHover: {\n                                            scale: 1.1\n                                        },\n                                        whileTap: {\n                                            scale: 0.9\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Cpu_ExternalLink_Link_Play_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-6 h-6 text-gray-600 dark:text-gray-300\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                        onClick: nextProject,\n                                        className: \"absolute right-4 top-1/2 -translate-y-1/2 w-12 h-12 bg-white dark:bg-gray-800 rounded-full shadow-lg flex items-center justify-center hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200 z-10\",\n                                        whileHover: {\n                                            scale: 1.1\n                                        },\n                                        whileTap: {\n                                            scale: 0.9\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Cpu_ExternalLink_Link_Play_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"w-6 h-6 text-gray-600 dark:text-gray-300\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, this),\n                    filteredProjects.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        className: \"flex justify-center space-x-2 mt-8\",\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        transition: {\n                            delay: 0.8\n                        },\n                        children: filteredProjects.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                onClick: ()=>setCurrentProject(index),\n                                className: \"w-3 h-3 rounded-full transition-all duration-300 \".concat(index === currentProject ? 'bg-gradient-to-r from-blue-600 to-electric-500 scale-125' : 'bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500'),\n                                whileHover: {\n                                    scale: 1.2\n                                },\n                                whileTap: {\n                                    scale: 0.9\n                                }\n                            }, index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                        lineNumber: 261,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        className: \"text-center mt-16 lg:mt-20\",\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8,\n                            delay: 0.4\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-gray-600 dark:text-gray-300 mb-8\",\n                                children: \"\\xbfListo para ser nuestro pr\\xf3ximo caso de \\xe9xito?\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                size: \"lg\",\n                                className: \"bg-gradient-to-r from-blue-600 to-electric-500 hover:from-blue-700 hover:to-electric-600 text-white font-semibold px-8 py-4 rounded-full shadow-xl hover:shadow-2xl transition-all duration-300\",\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Cpu_ExternalLink_Link_Play_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    href: \"#contacto\",\n                                    children: [\n                                        \"Iniciar Mi Proyecto\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Building_Calendar_ChevronLeft_ChevronRight_Cpu_ExternalLink_Link_Play_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"ml-2 w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\projects.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, this);\n}\n_s(ProjectsSection, \"OGC6bCZmR6xCKp4smftKUdaqSE4=\");\n_c = ProjectsSection;\nvar _c;\n$RefreshReg$(_c, \"ProjectsSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/projects.tsx\n"));

/***/ })

});