"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/sections/about.tsx":
/*!*******************************************!*\
  !*** ./src/components/sections/about.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AboutSection: () => (/* binding */ AboutSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Eye_Lightbulb_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Eye,Lightbulb,Shield,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Eye_Lightbulb_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Eye,Lightbulb,Shield,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Eye_Lightbulb_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Eye,Lightbulb,Shield,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Eye_Lightbulb_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Eye,Lightbulb,Shield,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Eye_Lightbulb_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Eye,Lightbulb,Shield,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Eye_Lightbulb_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Eye,Lightbulb,Shield,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Eye_Lightbulb_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Eye,Lightbulb,Shield,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _lib_data__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/data */ \"(app-pages-browser)/./src/lib/data.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ AboutSection auto */ \n\n\n\n\n\n\nconst values = [\n    {\n        icon: _barrel_optimize_names_ArrowRight_Eye_Lightbulb_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: 'Innovación',\n        description: 'Buscamos constantemente nuevas tecnologías y metodologías para ofrecer soluciones de vanguardia.'\n    },\n    {\n        icon: _barrel_optimize_names_ArrowRight_Eye_Lightbulb_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        title: 'Confiabilidad',\n        description: 'Construimos relaciones duraderas basadas en la transparencia, calidad y cumplimiento de compromisos.'\n    },\n    {\n        icon: _barrel_optimize_names_ArrowRight_Eye_Lightbulb_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        title: 'Colaboración',\n        description: 'Trabajamos como socios estratégicos, integrando nuestro equipo con el de nuestros clientes.'\n    },\n    {\n        icon: _barrel_optimize_names_ArrowRight_Eye_Lightbulb_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        title: 'Excelencia',\n        description: 'Nos comprometemos con los más altos estándares de calidad en cada proyecto que emprendemos.'\n    }\n];\nconst milestones = [\n    {\n        year: '2015',\n        title: 'Inicio',\n        description: 'Primeros trabajos'\n    },\n    {\n        year: '2017',\n        title: 'Crecimiento',\n        description: 'Crecimiento y formación'\n    },\n    {\n        year: '2019',\n        title: 'Certificación ISO',\n        description: 'Instalaciones profesionales'\n    },\n    {\n        year: '2023',\n        title: 'Innovación',\n        description: 'Incorporación de IA y IoT'\n    },\n    {\n        year: '2024',\n        title: 'Liderazgo',\n        description: 'Reconocimiento como empresa líder'\n    },\n    {\n        year: '2025',\n        title: 'Futuro',\n        description: 'Nuevas tecnologías y mercados'\n    }\n];\nfunction AboutSection() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"nosotros\",\n        className: \"py-20 lg:py-32 bg-white dark:bg-gray-900 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 industrial-grid opacity-10\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-20 right-10 w-72 h-72 bg-electric-500/5 rounded-full blur-3xl\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-20 left-10 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        className: \"text-center mb-16 lg:mb-20\",\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                className: \"inline-flex items-center px-4 py-2 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 text-sm font-medium mb-6\",\n                                initial: {\n                                    opacity: 0,\n                                    scale: 0.8\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    scale: 1\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.2\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Eye_Lightbulb_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Conoce a Servindex\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 dark:text-white mb-6\",\n                                children: [\n                                    \"M\\xe1s de 10 A\\xf1os \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"gradient-text\",\n                                        children: \"Transformando\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 28\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"la Industria\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed\",\n                                children: \"Somos pioneros en automatizaci\\xf3n industrial en Extremadura, con un equipo apasionado por la innovaci\\xf3n y comprometido con el \\xe9xito de nuestros clientes.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center mb-20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: -50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-3xl font-bold text-gray-900 dark:text-white mb-6\",\n                                        children: \"Nuestra Historia\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4 text-lg text-gray-600 dark:text-gray-300 leading-relaxed\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Servindex nace en 2025 en Extremadura, fruto del esfuerzo de dos t\\xe9cnicos que iniciaron su camino en la automatizaci\\xf3n industrial hace m\\xe1s de diez a\\xf1os.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 11\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"A lo largo de nuestro recorrido hemos participado en proyectos de alto nivel, integrando soluciones que optimizan procesos y mejoran la productividad de nuestros clientes.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 11\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Hoy seguimos creciendo, llevando nuestra experiencia a cada instalaci\\xf3n y ofreciendo un servicio cercano, profesional y adaptado a las necesidades de cada empresa.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 11\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                className: \"relative\",\n                                initial: {\n                                    opacity: 0,\n                                    x: 50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: milestones.map((milestone, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                            className: \"flex items-start space-x-4\",\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: index * 0.1\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 w-16 h-16 bg-gradient-to-br from-blue-600 to-electric-500 rounded-full flex items-center justify-center text-white font-bold\",\n                                                    children: milestone.year.slice(-2)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                                            children: milestone.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                                            lineNumber: 145,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 dark:text-gray-300\",\n                                                            children: milestone.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                                            lineNumber: 148,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 mb-20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                className: \"bg-gradient-to-br from-blue-50 to-electric-50 dark:from-blue-900/20 dark:to-electric-900/20 rounded-2xl p-8\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                whileHover: {\n                                    scale: 1.02\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gradient-to-br from-blue-600 to-electric-500 rounded-xl flex items-center justify-center mr-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Eye_Lightbulb_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-6 h-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                                children: \"Nuestra Misi\\xf3n\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-gray-600 dark:text-gray-300 leading-relaxed\",\n                                        children: \"Transformar la industria mediante soluciones de automatizaci\\xf3n innovadoras, eficientes y sostenibles que impulsen el crecimiento y la competitividad de nuestros clientes.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                className: \"bg-gradient-to-br from-electric-50 to-blue-50 dark:from-electric-900/20 dark:to-blue-900/20 rounded-2xl p-8\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.2\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                whileHover: {\n                                    scale: 1.02\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gradient-to-br from-electric-500 to-blue-600 rounded-xl flex items-center justify-center mr-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Eye_Lightbulb_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-6 h-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                                children: \"Nuestra Visi\\xf3n\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-gray-600 dark:text-gray-300 leading-relaxed\",\n                                        children: \"Ser la empresa de referencia en automatizaci\\xf3n industrial en el sur de Europa, reconocida por nuestra excelencia t\\xe9cnica, innovaci\\xf3n constante y compromiso con el \\xe9xito de nuestros clientes.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        className: \"mb-20\",\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-3xl font-bold text-gray-900 dark:text-white mb-4\",\n                                        children: \"Nuestros Valores\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-gray-600 dark:text-gray-300\",\n                                        children: \"Los principios que gu\\xedan cada decisi\\xf3n y acci\\xf3n en Servindex\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                                children: values.map((value, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                        className: \"text-center group\",\n                                        initial: {\n                                            opacity: 0,\n                                            y: 30\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: index * 0.1\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        whileHover: {\n                                            y: -10\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                                className: \"w-16 h-16 bg-gradient-to-br from-blue-600 to-electric-500 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300\",\n                                                whileHover: {\n                                                    rotate: 360\n                                                },\n                                                transition: {\n                                                    duration: 0.6\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(value.icon, {\n                                                    className: \"w-8 h-8 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-xl font-bold text-gray-900 dark:text-white mb-3\",\n                                                children: value.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 dark:text-gray-300 leading-relaxed\",\n                                                children: value.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-3xl font-bold text-gray-900 dark:text-white mb-4\",\n                                        children: \"Nuestro Equipo\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-gray-600 dark:text-gray-300\",\n                                        children: \"Profesionales apasionados por la innovaci\\xf3n y la excelencia\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-8 mb-12\",\n                                children: _lib_data__WEBPACK_IMPORTED_MODULE_2__.teamMembers.map((member, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                        className: \"group\",\n                                        initial: {\n                                            opacity: 0,\n                                            y: 30\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: index * 0.1\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        whileHover: {\n                                            y: -10\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-electric-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative mb-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-24 h-24 mx-auto rounded-full overflow-hidden\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            src: member.image,\n                                                            alt: member.name,\n                                                            width: 96,\n                                                            height: 96,\n                                                            className: \"w-full h-full object-cover grayscale group-hover:grayscale-0 transition-all duration-300\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-xl font-bold text-gray-900 dark:text-white mb-2\",\n                                                            children: member.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                                            lineNumber: 293,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-blue-600 dark:text-electric-400 font-medium mb-3\",\n                                                            children: member.position\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                                            lineNumber: 296,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 dark:text-gray-300 text-sm leading-relaxed\",\n                                                            children: member.bio\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                                            lineNumber: 299,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, member.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-gray-600 dark:text-gray-300 mb-8\",\n                                        children: \"\\xbfQuieres formar parte de nuestro equipo?\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        size: \"lg\",\n                                        className: \"bg-gradient-to-r from-blue-600 to-electric-500 hover:from-blue-700 hover:to-electric-600 text-white font-semibold px-8 py-4 rounded-full shadow-xl hover:shadow-2xl transition-all duration-300\",\n                                        children: [\n                                            \"Ver Oportunidades\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Eye_Lightbulb_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"ml-2 w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Servindex-v100\\\\src\\\\components\\\\sections\\\\about.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\n_c = AboutSection;\nvar _c;\n$RefreshReg$(_c, \"AboutSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/about.tsx\n"));

/***/ })

});