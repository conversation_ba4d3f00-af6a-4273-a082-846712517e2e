"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/data.ts":
/*!*************************!*\
  !*** ./src/lib/data.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   blogPosts: () => (/* binding */ blogPosts),\n/* harmony export */   projects: () => (/* binding */ projects),\n/* harmony export */   services: () => (/* binding */ services),\n/* harmony export */   statistics: () => (/* binding */ statistics),\n/* harmony export */   teamMembers: () => (/* binding */ teamMembers)\n/* harmony export */ });\nconst services = [\n    {\n        id: '1',\n        title: 'Automatización Industrial',\n        description: 'Sistemas de automatización completos para optimizar procesos industriales y aumentar la eficiencia operativa.',\n        icon: 'Settings',\n        features: [\n            'Diseño de sistemas automatizados',\n            'Integración de sensores y actuadores',\n            'Optimización de procesos',\n            'Reducción de costos operativos'\n        ]\n    },\n    {\n        id: '2',\n        title: 'Robótica Industrial',\n        description: 'Implementación de soluciones robóticas avanzadas para manufactura y manipulación de materiales.',\n        icon: 'Bot',\n        features: [\n            'Robots colaborativos (cobots)',\n            'Sistemas de visión artificial',\n            'Programación de trayectorias',\n            'Mantenimiento predictivo'\n        ]\n    },\n    {\n        id: '3',\n        title: 'Programación PLC/SCADA',\n        description: 'Desarrollo y programación de sistemas de control distribuido para monitoreo y control de procesos industriales.',\n        icon: 'Code',\n        features: [\n            'Programación de PLCs',\n            'Desarrollo de interfaces SCADA',\n            'Sistemas de alarmas',\n            'Históricos y reportes'\n        ]\n    },\n    {\n        id: '4',\n        title: 'Cuadros Eléctricos',\n        description: 'Diseño, fabricación e instalación de cuadros eléctricos industriales con los más altos estándares de calidad.',\n        icon: 'Zap',\n        features: [\n            'Diseño según normativas',\n            'Fabricación personalizada',\n            'Instalación y puesta en marcha',\n            'Certificaciones de calidad'\n        ]\n    },\n    {\n        id: '5',\n        title: 'Consultoría Técnica',\n        description: 'Asesoramiento especializado en ingeniería industrial, optimización de procesos y transformación digital.',\n        icon: 'Users',\n        features: [\n            'Auditorías técnicas',\n            'Planes de modernización',\n            'Análisis de eficiencia',\n            'Formación especializada'\n        ]\n    },\n    {\n        id: '6',\n        title: 'Mantenimiento Industrial',\n        description: 'Servicios integrales de mantenimiento preventivo, predictivo y correctivo para equipos industriales.',\n        icon: 'Wrench',\n        features: [\n            'Mantenimiento preventivo',\n            'Diagnóstico predictivo',\n            'Reparaciones especializadas',\n            'Gestión de repuestos'\n        ]\n    }\n];\nconst projects = [\n    {\n        id: '1',\n        title: 'Automatización de Línea de Tomate Concentrado',\n        description: 'Optimización completa de la línea de producción de tomate concentrado mediante control automatizado y monitoreo en tiempo real.',\n        image: '/imagenes/1.jpg',\n        category: 'Alimentaria',\n        technologies: [\n            'Siemens Step7',\n            'Profibus',\n            'Intouch',\n            'SCADA',\n            'PLC'\n        ],\n        year: 2025,\n        client: 'Conesa Group'\n    },\n    {\n        id: '2',\n        title: 'Producción Automatizada de Tomate Frito',\n        description: 'Automatización integral del proceso de tomate frito, desde la cocción hasta el envasado final, garantizando calidad constante.',\n        image: '/imagenes/7.jpg',\n        category: 'Alimentaria',\n        technologies: [\n            'CX-Programmer',\n            'CX-Supervisor',\n            'Weidmuller',\n            'Instrumentación'\n        ],\n        year: 2024,\n        client: 'Tomcoex'\n    },\n    {\n        id: '3',\n        title: 'Sistema de Filtrado de Arena Industrial',\n        description: 'Automatización de filtrado industrial con control de presión y ciclos de retrolavado automáticos para mayor eficiencia.',\n        image: '/imagenes/6.jpg',\n        category: 'Fluidos',\n        technologies: [\n            'PLC Omron',\n            'CX-Supervisor',\n            'HMI Proface',\n            'Válvulas Automáticas'\n        ],\n        year: 2025,\n        client: 'Agraz'\n    },\n    {\n        id: '4',\n        title: 'Línea Automatizada de Pelado de Almendras',\n        description: 'Control automático de rodillos y detección de obstrucciones para un pelado seguro y eficiente.',\n        image: '/imagenes/5.jpg',\n        category: 'Alimentaria',\n        technologies: [\n            'PLC Omron',\n            'HMI Proface',\n            'Variadores de Frecuencia'\n        ],\n        year: 2024,\n        client: 'Acopaex'\n    },\n    {\n        id: '5',\n        title: 'Producción Automatizada de Sustratos',\n        description: 'Línea de mezclado y envasado de sustratos con control de pesaje, humedad y flujo de material optimizado.',\n        image: '/imagenes/3.jpg',\n        category: 'Industrial',\n        technologies: [\n            'Siemens S7-1200',\n            'Profinet',\n            'Arrancador Schneider',\n            'Variadores'\n        ],\n        year: 2023,\n        client: 'Sustratos de Extremadura'\n    },\n    {\n        id: '6',\n        title: 'Pasadoras y Refinadoras de Tomate Automatizadas',\n        description: 'Control preciso de caudal y proceso en pasadoras y refinadoras para garantizar la calidad del producto final.',\n        image: '/imagenes/8.jpg',\n        category: 'Alimentaria',\n        technologies: [\n            'PLC Omron',\n            'HMI Proface',\n            'Variadores de Frecuencia'\n        ],\n        year: 2024,\n        client: 'Conesa Group'\n    },\n    {\n        id: '7',\n        title: 'Sistema de Extracción de gases',\n        description: 'Automatización de ventilación industrial con control de caudal, presión y alarmas de seguridad en tiempo real.',\n        image: '/imagenes/4.jpg',\n        category: 'Fluidos',\n        technologies: [\n            'PLC Simenes 1500',\n            'Redundancia',\n            'HMI Unified',\n            'Valvulas Automáticas'\n        ],\n        year: 2024,\n        client: 'Diamond Foundry'\n    }\n];\nconst blogPosts = [\n    {\n        id: '1',\n        title: 'El Futuro de la Automatización Industrial',\n        excerpt: 'Exploramos las tendencias emergentes en automatización y cómo están transformando la industria.',\n        content: '',\n        image: 'https://picsum.photos/800/400?random=4',\n        author: 'Equipo Servindex',\n        publishedAt: '2024-01-15',\n        category: 'Automatización',\n        tags: [\n            'Industria 4.0',\n            'IoT',\n            'Automatización'\n        ],\n        readTime: 5\n    },\n    {\n        id: '2',\n        title: 'Mantenimiento Predictivo con IA',\n        excerpt: 'Cómo la inteligencia artificial está revolucionando el mantenimiento industrial.',\n        content: '',\n        image: 'https://picsum.photos/800/400?random=5',\n        author: 'Equipo Servindex',\n        publishedAt: '2024-01-10',\n        category: 'Mantenimiento',\n        tags: [\n            'IA',\n            'Mantenimiento',\n            'Predictivo'\n        ],\n        readTime: 7\n    }\n];\nconst teamMembers = [\n    {\n        id: '1',\n        name: 'Pablo Rebollo',\n        position: 'Director Técnico',\n        image: 'https://picsum.photos/400/400?random=6',\n        bio: 'Técnico polivalente en todas las áreas de la A.'\n    },\n    {\n        id: '2',\n        name: 'Jaime Villafruela',\n        position: 'Director Técnico',\n        image: 'https://picsum.photos/400/400?random=7',\n        bio: 'Especialista en sistemas PLC/SCADA con amplia experiencia en proyectos de gran envergadura.'\n    }\n];\nconst statistics = [\n    {\n        id: '1',\n        value: 50,\n        label: 'Proyectos Completados',\n        suffix: '+'\n    },\n    {\n        id: '2',\n        value: 98,\n        label: 'Satisfacción del Cliente',\n        suffix: '%'\n    },\n    {\n        id: '3',\n        value: 10,\n        label: 'Años de Experiencia',\n        suffix: '+'\n    },\n    {\n        id: '4',\n        value: 24,\n        label: 'Soporte Técnico',\n        suffix: '/7'\n    }\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/data.ts\n"));

/***/ })

});